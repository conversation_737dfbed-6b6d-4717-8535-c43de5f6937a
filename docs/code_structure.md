# 代码结构设计文档

## 概述

本文档描述了项目的代码结构设计，特别是关于日志和配置系统的设计。通过合理的架构设计，解决了循环依赖问题，提高了代码的可维护性和灵活性。

## 模块层次结构

项目的核心模块按以下层次结构组织：

1. **基础层 (core/base.py)**
   - 提供基本的日志和配置功能
   - 不依赖项目中的其他模块
   - 被其他模块所依赖

2. **功能层**
   - **日志系统 (core/logger.py)**
     - 依赖基础层
     - 提供高级日志功能
   - **配置系统 (core/config.py)**
     - 依赖基础层
     - 提供高级配置管理功能

3. **业务层**
   - **爬虫基类 (spiders/base/)**
     - 依赖功能层
     - 提供爬虫相关基础功能
   - **业务模块 (spiders/)**
     - 依赖爬虫基类
     - 实现具体业务逻辑

## 依赖关系

通过基础层的引入，打破了原有的循环依赖问题：

```
旧的依赖关系:
config.py <---> logger.py  (循环依赖)
      ^          ^
      |          |
    spider.py   auth.py

新的依赖关系:
      base.py
     /      \
config.py  logger.py
     ^      ^
     |      |
   spider.py, auth.py
```

## 核心类说明

### BaseObject

`BaseObject` 是一个基础类，提供了基本的日志功能。所有需要日志功能的类都应该继承此类。

```python
from core.base import BaseObject

class MyClass(BaseObject):
    def __init__(self):
        super().__init__()
        self.logger.info("初始化完成")
```

### Logger

`Logger` 类是 `BaseObject` 的扩展，提供了更高级的日志功能，如文件日志、日志轮转等。

```python
from core.logger import Logger

class MyClass(Logger):
    def __init__(self):
        super().__init__()
        self.logger.info("初始化完成")
```

### ConfigManager

`ConfigManager` 类继承自 `BaseObject`，负责配置文件的加载和管理。

```python
from core.config import ConfigManager

config = ConfigManager("config.yaml")
value = config.get("section.key", "default_value")
```

## 初始化流程

1. 创建 `ConfigManager` 实例加载配置
2. 使用 `configure_logging` 函数初始化日志系统
3. 其他模块使用配置和日志系统

示例:

```python
from core.config import ConfigManager
from core.logger import configure_logging

# 加载配置
config = ConfigManager("config.yaml")

# 初始化日志系统
configure_logging(config)

# 使用日志
import logging
logger = logging.getLogger("main")
logger.info("系统启动")
```

## 注意事项

1. 新增的类应该继承 `BaseObject` 而不是直接使用 `logging` 模块
2. 配置对象应该在日志系统初始化之前创建
3. 避免在模块级别直接使用 `ConfigManager` 或 `Logger` 进行初始化，以防止循环依赖 