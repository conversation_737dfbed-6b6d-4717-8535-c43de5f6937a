# 日志系统使用指南

本文档描述了项目中标准化的日志使用方法，请所有开发人员遵循此规范进行日志打印。

## 基本原则

1. 所有需要打印日志的类都应该继承 `Logger` 基类
2. 使用 `self.logger` 进行日志打印，而不是直接使用 `logging.getLogger()`
3. 日志级别应合理使用，符合项目规范

## 日志级别使用指南

- **DEBUG**: 详细的调试信息，仅在开发和调试环境使用
- **INFO**: 正常的系统运行信息，例如爬虫开始爬取、完成爬取等
- **WARNING**: 警告信息，例如数据异常但不影响系统运行
- **ERROR**: 错误信息，例如API调用失败、网络连接超时等
- **CRITICAL**: 严重错误，例如系统崩溃、无法恢复的错误

## 使用方法

### 1. 在类中使用日志

```python
from core.logger import Logger

class MyClass(Logger):
    def __init__(self):
        # 必须调用父类初始化方法
        super().__init__()
        
        # 现在可以使用self.logger
        self.logger.info("MyClass已初始化")
        
    def do_something(self):
        try:
            # 业务逻辑
            result = complex_operation()
            self.logger.info(f"操作完成，结果: {result}")
            return result
        except Exception as e:
            self.logger.error(f"操作失败: {str(e)}", exc_info=True)
            raise
```

### 2. 多重继承的情况

如果需要同时继承其他基类，可以使用多重继承：

```python
from core.logger import Logger
from another_module import AnotherBaseClass

class MyClass(Logger, AnotherBaseClass):
    def __init__(self):
        # 必须调用Logger基类的初始化方法
        super().__init__()
        
        # 业务代码
        self.logger.info("初始化完成")
```

### 3. 修改日志名称

默认情况下，日志器的名称为类名，如果需要自定义日志名称：

```python
class MyClass(Logger):
    def __init__(self):
        # 自定义日志名称
        super().__init__(logger_name="my_custom_logger")
        
        self.logger.info("使用自定义日志名初始化")
```

## 日志格式

项目中的日志格式为：

```
%(asctime)s - %(name)s - %(levelname)s - %(message)s
```

示例：
```
2023-05-20 14:30:45,123 - MyClass - INFO - 操作成功完成
```

## 日志文件

日志文件存储在 `logs` 目录下，按照类名和日期命名：

```
logs/
  ├── MyClass_20230520.log
  ├── api_20230520.log
  ├── core_20230520.log
  └── spiders_20230520.log
```

## 常见问题

### 为什么要使用 `self.logger` 而不是直接使用 `logging.getLogger()`?

使用 `self.logger` 有以下好处：

1. 统一管理日志配置，避免重复配置
2. 确保日志级别、格式一致
3. 方便根据模块名进行日志分类
4. 避免日志重复打印

### 如何防止日志重复打印？

在 `Logger` 基类中，我们已经处理了日志重复打印的问题。确保：

1. 不要在同一个类中多次初始化日志器
2. 不要同时使用 `logging.basicConfig()` 和 `Logger` 基类
3. 如果必须使用 `logging` 模块直接获取日志器，请使用 `core.logger.get_logger()` 函数

### 如何设置日志级别？

日志级别可以在配置文件 `conf/config.yaml` 中设置：

```yaml
logging:
  level: INFO  # 可选：DEBUG, INFO, WARNING, ERROR, CRITICAL
```

也可以在命令行参数中指定：

```bash
python main.py --log-level=DEBUG
``` 