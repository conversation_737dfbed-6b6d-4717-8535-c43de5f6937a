# 使用官方 Ubuntu 22.04 基础镜像
FROM ubuntu:22.04

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PLAYWRIGHT_BROWSERS_PATH=/ms-playwright \
    DEBIAN_FRONTEND=noninteractive \
    PLAYWRIGHT_HEADLESS=true

# 配置镜像源（阿里云）
RUN sed -i 's/archive.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    apt-get update

# 安装系统依赖（使用系统自带 Python）
RUN apt-get install -y --no-install-recommends \
    python3 \
    python3-venv \
    python3-pip \
    wget \
    gnupg2 \
    cron \
    rsyslog \
    ca-certificates \
    curl \
    locales \
    fonts-noto-cjk \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgbm1 \
    libgtk-4-0 \
    libnspr4 \
    libnss3 \
    libwayland-client0 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxkbcommon0 \
    libxrandr2 \
    libgdal32 \
    xdg-utils \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 配置 Python 别名（将 python3 映射到 python）
RUN update-alternatives --install /usr/bin/python python /usr/bin/python3 1

# 设置中文环境
RUN locale-gen zh_CN.UTF-8
ENV LANG=zh_CN.UTF-8 \
    LANGUAGE=zh_CN:zh \
    LC_ALL=zh_CN.UTF-8

# 复制项目文件
COPY . /app/

# 安装 Python 依赖
RUN python -m pip install --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt \
    && pip install --no-cache-dir playwright \
    && playwright install chromium --with-deps

# 创建必要的目录
RUN mkdir -p /app/logs /app/output /app/storage/auth /app/traces

# 配置定时任务
RUN echo "0,30 8-18 * * * cd /app && /app/start.sh -e test >> /app/logs/cron.log 2>&1" > /etc/cron.d/news-spider \
    && chmod 0644 /etc/cron.d/news-spider \
    && crontab /etc/cron.d/news-spider

# 设置执行权限
RUN chmod +x /app/start.sh /app/docker/entrypoint.sh

# 入口点配置
ENTRYPOINT ["/app/docker/entrypoint.sh"]

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1
