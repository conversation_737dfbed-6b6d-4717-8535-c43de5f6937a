#!/bin/bash
#
# Docker容器入口点脚本
# 启动cron服务和其他必要服务
#

# 启用命令回显
set -x

# 启动rsyslog以支持cron日志
service rsyslog start

# 启动cron服务
service cron start

# 检查是否需要立即执行爬虫
if [ "$1" = "run-now" ]; then
    echo "立即运行爬虫任务..."
    cd /app && ./start.sh
    exit $?
fi

# 显示cron作业
echo "当前配置的cron作业:"
crontab -l

# 输出日志到标准输出
echo "启动日志监控..."
touch /app/logs/cron.log
tail -f /app/logs/cron.log &

# 保持容器运行
echo "容器已启动，cron任务将按计划执行..."
exec "$@"

# 如果没有指定命令，则保持容器运行
if [ $# -eq 0 ]; then
    # 睡眠循环保持容器运行
    while true; do
        sleep 60
        # 可以在这里添加容器健康检查逻辑
    done
fi 