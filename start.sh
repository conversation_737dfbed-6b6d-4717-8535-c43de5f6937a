#!/bin/bash
#
# 特斯拉工单爬虫启动脚本
# 用于启动爬虫任务并记录日志
#

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -e, --env ENV      指定运行环境 (dev/test/prod)"
    echo "  -l, --log LEVEL    指定日志级别 (DEBUG/INFO/WARNING/ERROR/CRITICAL)"
    echo "  -h, --help         显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                 # 使用默认配置运行（开发环境）"
    echo "  $0 -e prod        # 在生产环境运行"
    echo "  $0 -e test -l DEBUG # 在测试环境运行，使用DEBUG日志级别"
}

# 解析命令行参数
ENV="dev"
LOG_LEVEL="DEBUG"

while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENV="$2"
            shift 2
            ;;
        -l|--log)
            LOG_LEVEL="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "错误: 未知选项 $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证环境参数
if [[ ! "$ENV" =~ ^(dev|test|prod)$ ]]; then
    echo "错误: 无效的环境参数 '$ENV'"
    echo "有效的环境参数: dev, test, prod"
    exit 1
fi

# 验证日志级别参数
if [[ ! "$LOG_LEVEL" =~ ^(DEBUG|INFO|WARNING|ERROR|CRITICAL)$ ]]; then
    echo "错误: 无效的日志级别 '$LOG_LEVEL'"
    echo "有效的日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL"
    exit 1
fi

# 定义变量
PROJECT_ROOT=$(cd "$(dirname "$0")" && pwd)
VENV_PATH="$PROJECT_ROOT/.venv"
LOG_DIR="$PROJECT_ROOT/logs"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$LOG_DIR/spider_${ENV}_$TIMESTAMP.log"
MAIN_SCRIPT="$PROJECT_ROOT/main.py"

# 确保日志目录存在
mkdir -p $LOG_DIR

# 记录启动时间和环境信息
echo "===== 爬虫任务开始 $(date) =====" | tee -a $LOG_FILE
echo "工作目录: $PROJECT_ROOT" | tee -a $LOG_FILE
echo "运行环境: $ENV" | tee -a $LOG_FILE
echo "日志级别: $LOG_LEVEL" | tee -a $LOG_FILE
echo "Python虚拟环境: $VENV_PATH" | tee -a $LOG_FILE
echo "系统信息: $(uname -a)" | tee -a $LOG_FILE
echo "" | tee -a $LOG_FILE

# 检查虚拟环境是否存在
if [ ! -f "$VENV_PATH/bin/activate" ]; then
    echo "错误: 虚拟环境不存在，尝试创建..." | tee -a $LOG_FILE
    python3.9 -m venv "$VENV_PATH"
    if [ $? -ne 0 ]; then
        echo "错误: 无法创建虚拟环境，请先运行deploy.sh脚本" | tee -a $LOG_FILE
        exit 1
    fi
fi

# 激活虚拟环境
source "$VENV_PATH/bin/activate"

# 验证虚拟环境是否正确激活
if [ -z "$VIRTUAL_ENV" ]; then
    echo "错误: 虚拟环境激活失败" | tee -a $LOG_FILE
    exit 1
fi

echo "已激活虚拟环境: $VIRTUAL_ENV" | tee -a $LOG_FILE

# 输出Python版本和环境信息
echo "Python 路径: $(which python)" | tee -a $LOG_FILE
echo "Python 版本: $(python --version 2>&1)" | tee -a $LOG_FILE
echo "Pip 版本: $(pip --version)" | tee -a $LOG_FILE
echo "" | tee -a $LOG_FILE

# 设置无头模式的环境变量
#export PLAYWRIGHT_BROWSERS_PATH="$PROJECT_ROOT/.playwright-browsers"
export PYTHONIOENCODING=utf-8
export PYTHONUNBUFFERED=1
export PLAYWRIGHT_HEADLESS=true

# 在运行主程序前添加包兼容性检查
echo "检查依赖包兼容性..." | tee -a $LOG_FILE
# 检查numpy和pandas是否已安装且兼容
if ! python -c "import numpy; import pandas" 2>/dev/null; then
    echo "检测到numpy或pandas导入错误，尝试修复..." | tee -a $LOG_FILE
    # 卸载现有包
    pip uninstall -y numpy pandas
    # 安装兼容版本
    pip install numpy==1.24.4
    pip install pandas
    echo "已重新安装numpy和pandas" | tee -a $LOG_FILE
fi

# 运行主程序
echo "启动爬虫程序..." | tee -a $LOG_FILE
python "$MAIN_SCRIPT" --spider tesla --env "$ENV" --log-level "$LOG_LEVEL" | tee -a $LOG_FILE
EXIT_CODE=$?

# 检查退出状态
if [ $EXIT_CODE -eq 0 ]; then
    echo "爬虫程序正常完成。" | tee -a $LOG_FILE
else
    echo "爬虫程序异常退出，退出码: $EXIT_CODE" | tee -a $LOG_FILE
    
    # 可以在这里添加发送错误通知的代码
    # 例如发送邮件或钉钉通知
fi

# 记录结束时间
echo "===== 爬虫任务结束 $(date) =====" | tee -a $LOG_FILE

# 清理过期日志（保留最近7天的日志）
find $LOG_DIR -name "spider_*.log" -type f -mtime +7 -delete

# 退出虚拟环境
deactivate

exit $EXIT_CODE 