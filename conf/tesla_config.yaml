# 特斯拉爬虫配置文件

# 登录配置
login:
  url: "https://partners.tesla.cn/home/<USER>"  # 登录URL
  username: "<EMAIL>"  # 用户名
  password: "Mrgsysslmm1."  # 密码
  remember_me: true  # 是否记住登录状态
  storage_state_path: "storage/auth/tesla_auth.json"  # 存储状态路径
  max_login_retries: 3  # 最大登录重试次数

# 工单系统配置
tesla_pp:
  base_url: "https://akamai-apigateway-partnerportalcn.tesla.cn"  # 工单系统基础URL
  order_list_url: "https://akamai-apigateway-partnerportalcn.tesla.cn/installerapi/installation/search"  # 工单列表URL
  order_detail_url: "https://akamai-apigateway-partnerportalcn.tesla.cn/installerapi/installation/detail"  # 工单详情URL模板
  order_status_url: "https://akamai-apigateway-partnerportalcn.tesla.cn/installerapi/installation/status/accept"  # 工单状态更新URL
  
  # 获取工单的参数配置
  fetch:
    page_size: 100  # 每页获取的数量
    timeout: 60  # 获取超时时间，秒

# 报表配置
report:
  output_file: "output"  # 输出文件名模板，支持{date}变量
  sheet_name: "特斯拉工单"  # 工作表名

# 邮件通知配置 (覆盖全局配置)
email:
  smtp_server: "smtp.163.com"
  smtp_port: 465
  smtp_ssl: true
  sender_email: "<EMAIL>"
  sender_password: "VZcZ4zuLPs3GRGxy"
  sender_name: "特斯拉工单"
  receiver_test_emails:
    - "<EMAIL>"
  receiver_emails:
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"