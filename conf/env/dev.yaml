# 开发环境配置文件

# 日志配置
logging:
  level: DEBUG  # 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
  console_output: true  # 开发环境控制台输出开启

# 数据库配置 - 开发环境
database:
  url: "mysql+pymysql://root:123456@localhost:3306/test"

# News系统API配置 - 开发环境
news_api:
  create_url: "http://localhost:8081/rrs/dmj/openapi/news/spider/create"  # 本地开发API
  timeout: 60
  auth_token: "1053ce1c42e74370a77671dfe2d37cac"
  max_workers: 3
  batch_size: 5
  batch_delay: 2
  request_delay: 0.3

# 消息队列配置 - 开发环境
RabbitMQ:
  host: localhost
  port: 5672
  vhost: /
  username: guest
  password: guest

# 浏览器配置 - 开发环境
browser:
  headless: false  # 开发环境可视化调试
  slow_mo: 100  # 增加延迟，便于观察 