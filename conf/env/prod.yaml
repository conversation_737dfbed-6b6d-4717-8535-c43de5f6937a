# 生产环境配置文件

# 日志配置
logging:
  level: DEBUG  # 生产环境只记录警告和错误
  console_output: true  # 生产环境不输出到控制台
  backup_count: 30  # 保留更多的日志文件

# 数据库配置 - 生产环境
database:
  url: "mysql+pymysql://bonc:Bonc1234*@**************:3306/rrs_pro"

# News系统API配置 - 生产环境
news_api:
  create_url: "https://gd.rrskjfw.com.cn/rrs-ticket/rrs/dmj/openapi/news/spider/create"
  timeout: 60  # 生产环境超时时间更长
  auth_token: "4df6999b3e91466ca9e195b55b205ca4"
  max_workers: 3
  batch_size: 5
  batch_delay: 2
  request_delay: 0.3

# 消息队列配置 - 生产环境
RabbitMQ:
  host: prod-rabbitmq.example.com
  port: 5672
  vhost: /
  username: prod_user
  password: prod_pass
  connection_attempts: 5  # 增加连接尝试次数
  retry_delay: 10  # 增加重试延迟

# 浏览器配置 - 生产环境
browser:
  headless: true  # 生产环境使用无头模式
  slow_mo: 0  # 不增加延迟
  enable_tracing: false  # 禁用跟踪

# 飞书通知配置 - 生产环境
feiShu:
  enable: true  # 生产环境启用通知
  webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/prod-webhook-token"
  secret: "prod_secret_key" 