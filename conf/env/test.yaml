# 测试环境配置文件

# 日志配置
logging:
  level: INFO  # 测试环境日志级别INFO
  console_output: true

# 数据库配置 - 测试环境
database:
  url: "mysql+pymysql://root:G5#df87rt!qw@*************:3306/rrs_pro"

# News系统API配置 - 测试环境
news_api:
  create_url: "https://uat.rrskjfw.com.cn/rrs-ticket/rrs/dmj/openapi/news/spider/create"
  timeout: 60
  auth_token: "1053ce1c42e74370a77671dfe2d37cac"
  max_workers: 3
  batch_size: 5
  batch_delay: 2
  request_delay: 0.3

# 消息队列配置 - 测试环境
RabbitMQ:
  host: test-rabbitmq.example.com
  port: 5672
  vhost: /
  username: test_user
  password: test_pass

# 浏览器配置 - 测试环境
browser:
  headless: true  # 测试环境使用无头模式
  slow_mo: 50 