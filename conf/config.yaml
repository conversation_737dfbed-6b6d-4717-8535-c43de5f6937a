# 车企工单爬虫系统主配置文件

# 当前环境设置
env: dev  # 环境设置: dev(开发环境), test(测试环境), prod(生产环境)

# 日志配置
logging:
  level: DEBUG  # 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
  storage_dir: logs  # 日志存储目录
  filename_format: "%Y-%m-%d_%H-%M-%S.log"  # 日志文件名格式，使用time.strftime格式
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"  # 日志格式
  max_bytes: 10485760  # 每个日志文件最大大小，默认10MB
  backup_count: 5  # 保留的历史日志文件数量
  console_output: true  # 是否输出到控制台

# 浏览器配置
browser:
  type: chromium  # 浏览器类型，可选：chromium, firefox, webkit
  headless: true  # 是否无头模式
  slow_mo: 50  # 操作延迟时间，毫秒
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"  # 用户代理
  viewport:
    width: 1366  # 视口宽度
    height: 768  # 视口高度
  enable_tracing: false  # 是否启用跟踪
  trace_path: "traces/browser_trace.zip"  # 跟踪文件路径
  trace_options:
    screenshots: true  # 是否跟踪截图
    snapshots: true  # 是否跟踪快照
    sources: true  # 是否跟踪源码

# HTTP请求配置
http:
  timeout: 30  # 超时时间，秒
  max_retries: 3  # 最大重试次数
  retry_backoff_factor: 0.3  # 重试退避因子
  retry_status_forcelist:  # 需要重试的状态码列表
    - 500
    - 502
    - 503
    - 504
  verify_ssl: true  # 是否验证SSL证书
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"  # 用户代理

# 消息队列配置
RabbitMQ:
  host: localhost  # RabbitMQ主机
  port: 5672  # RabbitMQ端口
  vhost: /  # 虚拟主机
  username: guest  # 用户名
  password: guest  # 密码
  exchange: spider_exchange  # 交换机名称
  exchange_type: topic  # 交换机类型
  queue: spider_queue  # 队列名称
  routing_key: spider.#  # 路由键
  connection_attempts: 3  # 连接尝试次数
  retry_delay: 5  # 重试延迟时间，秒
  notification_prefix: spider.notification  # 通知路由键前缀
  notification_queue: spider_notification_queue  # 通知队列名称
  task_prefix: spider.task  # 任务路由键前缀
  task_queue: spider_task_queue  # 任务队列名称
  event_prefix: spider.event  # 事件路由键前缀
  event_queue: spider_event_queue  # 事件队列名称

# 存储配置
storage:
  base_dir: storage  # 基础存储目录
  auth_dir: storage/auth  # 认证信息存储目录
  download_dir: storage/downloads  # 下载文件存储目录
  template_dir: storage/templates  # 模板文件存储目录

# 飞书通知配置
feiShu:
  enable: false  # 是否启用飞书通知
  webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-token"  # 飞书机器人Webhook
  secret: "your_secret"  # 飞书机器人密钥

# News系统API配置
news_api:
  create_url: "http://localhost:8081/rrs/dmj/openapi/news/spider/create"  # 创建工单API
  timeout: 120  # API超时时间，秒
  auth_token: "your_auth_token"  # API认证令牌