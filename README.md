# News Spider 爬虫系统

## 简介
News Spider是一个通用的爬虫系统，用于抓取各种车企工单系统的数据，并同步到News系统。

## 特性
- 支持多车企爬虫配置
- 多环境配置(开发、测试、生产)
- 自动同步数据到News系统
- 生成报表并支持邮件发送
- 日志系统和通知系统

## 环境配置
系统支持三种环境:
- `dev`: 开发环境 (默认)
- `test`: 测试环境
- `prod`: 生产环境

可以通过以下方式设置环境:
1. 在`conf/config.yaml`中设置`env`参数
2. 通过命令行参数`--env`指定

环境特定的配置存储在`conf/env/`目录下:
- `conf/env/dev.yaml`: 开发环境配置
- `conf/env/test.yaml`: 测试环境配置
- `conf/env/prod.yaml`: 生产环境配置

环境配置会覆盖主配置文件中的相应设置。

## 安装

1. 克隆仓库：

```bash
git clone https://github.com/yourusername/news-spider.git
cd news-spider
```

2. 安装依赖：

```bash
pip install -r requirements.txt
```

3. 安装Playwright浏览器：

```bash
playwright install
```

4. 修改配置：
   - 编辑`conf/config.yaml`设置通用配置
   - 编辑`conf/tesla_config.yaml`设置特斯拉爬虫配置

## 使用
```bash
# 基本用法 (使用Tesla爬虫)
python main.py

# 指定环境
python main.py --env dev    # 开发环境
python main.py --env test   # 测试环境
python main.py --env prod   # 生产环境

# 指定日志级别
python main.py --log-level DEBUG

# 同步到News系统
python main.py --sync

# 生成报表
python main.py --report

# 完整参数
python main.py --help
```

## 配置文件

系统使用YAML配置文件，主要配置文件：

- `conf/config.yaml`: 通用配置
- `conf/tesla_config.yaml`: 特斯拉爬虫配置

### 特斯拉配置示例

```yaml
# 登录配置
login:
  url: "https://partners.tesla.cn/login"
  username: "your_username"
  password: "your_password"
  
# 工单系统配置
worksystem:
  base_url: "https://partners.tesla.cn/service"
  # ...更多配置
```

## 项目结构

```
news-spider/
├── conf/                   # 配置文件
│   ├── config.yaml         # 主配置文件
│   ├── env/                # 环境配置目录
│   │   ├── dev.yaml        # 开发环境配置
│   │   ├── test.yaml       # 测试环境配置
│   │   └── prod.yaml       # 生产环境配置
│   └── tesla_config.yaml   # 特斯拉爬虫配置
├── core/                   # 核心模块
│   ├── config.py           # 配置管理
│   ├── database.py         # 数据库管理
│   ├── logger.py           # 日志管理
│   ├── notifier/           # 通知模块
│   ├── messaging/          # 消息队列模块
│   └── utils/              # 工具类
├── spiders/                # 爬虫模块
│   ├── base/               # 基础类
│   │   ├── auth.py         # 认证基类
│   │   └── spider.py       # 爬虫基类
│   └── tesla/              # 特斯拉爬虫
│       ├── __init__.py     # 初始化文件
│       ├── tesla_api.py    # API模块
│       ├── tesla_auth.py   # 认证模块
│       ├── tesla_dao.py    # 数据访问模块
│       └── tesla_spider.py # 爬虫实现
├── storage/                # 存储目录
│   ├── auth/               # 认证信息存储
│   └── templates/          # 报表模板
├── output/                 # 输出文件目录
├── main.py                 # 主入口
└── requirements.txt        # 依赖项
```

## 开发指南

### 添加新爬虫

要添加新的爬虫支持，需要以下步骤：

1. 在`spiders`目录下创建新的爬虫包，例如`bmw`
2. 实现相应的认证、API、DAO和爬虫类
3. 在`conf`目录下创建特定的配置文件
4. 在`main.py`中添加对新爬虫的支持

### 注意事项

- 确保遵循网站的robots.txt规则
- 模拟人类行为避免被封锁
- 合理设置请求间隔
- 妥善保存认证信息

# 特斯拉工单爬虫项目

该项目用于自动化采集特斯拉工单系统的数据，并同步到News系统。

## 部署指南

本项目提供了多种部署方式，可以根据实际情况选择合适的方式进行部署。

### 方式一：直接部署（推荐用于开发环境）

```bash
# 克隆项目
git clone <项目仓库地址> news-spider
cd news-spider

# 执行部署脚本
sudo ./deploy.sh

# 部署完成后，可以手动执行爬虫
./start.sh -e test
```

### 方式二：Systemd服务部署（推荐用于生产环境）

```bash
# 克隆项目
git clone <项目仓库地址> news-spider
cd news-spider

# 安装systemd服务
sudo ./systemd/install-service.sh

# 查看服务状态
sudo systemctl status news-spider
```

### 方式三：Docker容器部署（推荐用于隔离环境）

```bash
# 克隆项目
git clone <项目仓库地址> news-spider
cd news-spider

# 使用Docker Compose构建并启动容器
docker-compose up -d

# 查看容器日志
docker logs -f news-spider

# 手动执行爬虫任务
docker exec news-spider /app/start.sh
```

## 项目结构

```
news-spider/
├── conf/                    # 配置文件目录
│   └── tesla_config.yaml    # 特斯拉爬虫配置
├── core/                    # 核心模块
├── spiders/                 # 爬虫模块
│   └── tesla/               # 特斯拉爬虫
├── docker/                  # Docker相关文件
│   ├── Dockerfile           # Docker构建文件
│   └── entrypoint.sh        # Docker入口脚本
├── systemd/                 # Systemd服务文件
│   ├── news-spider.service  # 系统服务定义
│   └── install-service.sh   # 服务安装脚本
├── deploy.sh                # 部署脚本
├── start.sh                 # 启动脚本
├── docker-compose.yml       # Docker Compose配置
└── main.py                  # 主程序入口
```

## 配置说明

爬虫的主要配置在 `conf/tesla_config.yaml` 文件中，包括登录凭据、API地址等。请根据实际环境修改配置。

## 定时任务

项目默认配置为每小时执行一次爬虫任务，可以通过以下方式修改定时计划：

- 直接部署：修改crontab
  ```bash
  crontab -e
  # 修改执行频率，例如每30分钟执行一次
  */30 * * * * cd /path/to/news-spider && ./start.sh >> /path/to/news-spider/logs/cron.log 2>&1
  ```

- Systemd服务：修改cron任务
  ```bash
  sudo nano /etc/cron.d/news-spider
  # 修改后保存并重新加载cron服务
  sudo systemctl restart cron
  ```

- Docker部署：修改docker-compose.yml和Dockerfile中的cron设置
  ```bash
  # 在Docker/Dockerfile中修改cron设置
  RUN echo "*/30 * * * * cd /app && /app/start.sh >> /app/logs/cron.log 2>&1" > /etc/cron.d/news-spider
  # 重新构建容器
  docker-compose up -d --build
  ```

## 日志查看

- 直接部署：日志位于 `logs` 目录
  ```bash
  tail -f logs/spider_*.log
  ```

- Systemd服务：使用journalctl查看
  ```bash
  sudo journalctl -u news-spider -f
  ```

- Docker部署：使用docker logs
  ```bash
  docker logs -f news-spider
  ```

## 故障排除

如果遇到问题，可以检查以下几点：

1. 检查配置文件是否正确
2. 查看日志文件了解详细错误信息
3. 确保网络连接正常
4. 检查特斯拉工单系统是否可访问
5. 验证账号密码是否正确

如需帮助，请联系项目维护者。

# 特斯拉工单爬虫系统

## 项目概述
这是一个用于爬取特斯拉工单信息的爬虫系统，支持多环境配置和灵活的日志管理。

## 功能特点
- 支持多环境配置（开发、测试、生产）
- 灵活的日志级别控制
- 自动化的依赖管理
- 完整的错误处理和通知机制
- 支持飞书通知集成

## 环境要求
- Python 3.9+
- 虚拟环境支持
- 操作系统：Linux/MacOS/Windows

## 目录结构
```
news-spider/
├── conf/                    # 配置文件目录
│   ├── config.yaml         # 主配置文件
│   └── env/                # 环境特定配置
│       ├── dev.yaml        # 开发环境配置
│       ├── test.yaml       # 测试环境配置
│       └── prod.yaml       # 生产环境配置
├── core/                   # 核心模块
│   ├── base.py            # 基础类
│   ├── config.py          # 配置管理
│   ├── logger.py          # 日志管理
│   └── notifier/          # 通知模块
├── spiders/               # 爬虫模块
│   └── tesla/             # 特斯拉爬虫
├── logs/                  # 日志目录
├── .venv/                 # Python虚拟环境
├── main.py               # 主程序入口
├── start.sh              # 启动脚本
└── README.md             # 项目文档
```

## 安装步骤
1. 克隆项目
```bash
git clone [项目地址]
cd news-spider
```

2. 创建并激活虚拟环境
```bash
python3.9 -m venv .venv
source .venv/bin/activate  # Linux/MacOS
# 或
.venv\Scripts\activate  # Windows
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

## 配置说明
### 环境配置
系统支持三种运行环境：
- 开发环境（dev）：用于本地开发和调试
- 测试环境（test）：用于功能测试和验证
- 生产环境（prod）：用于实际生产环境

每个环境都有独立的配置文件，位于`conf/env/`目录下。

### 日志配置
支持以下日志级别：
- DEBUG：详细的调试信息
- INFO：一般信息
- WARNING：警告信息
- ERROR：错误信息
- CRITICAL：严重错误信息

## 使用方法
### 使用启动脚本
启动脚本提供了便捷的命令行接口：

```bash
# 显示帮助信息
./start.sh --help

# 使用默认配置运行（开发环境）
./start.sh

# 在生产环境运行
./start.sh -e prod

# 在测试环境运行，使用DEBUG日志级别
./start.sh -e test -l DEBUG

# 使用长参数形式
./start.sh --env prod --log DEBUG
```

### 直接运行Python脚本
也可以直接运行Python脚本：

```bash
# 开发环境
python main.py --env dev

# 测试环境
python main.py --env test --log-level DEBUG

# 生产环境
python main.py --env prod --log-level INFO
```

## 日志管理
- 日志文件位于`logs/`目录
- 日志文件名格式：`spider_{环境}_{时间戳}.log`
- 自动清理30天前的日志文件
- 支持同时输出到控制台和文件

## 错误处理
- 程序异常退出时会记录详细的错误信息
- 支持通过飞书机器人发送错误通知
- 自动重试机制处理临时性错误

## 注意事项
1. 首次运行前请确保配置文件正确设置
2. 生产环境部署前请仔细检查配置
3. 建议定期检查日志文件大小
4. 确保有足够的磁盘空间存储日志

## 贡献指南
1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证
[许可证类型]
