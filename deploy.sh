#!/bin/bash
#
# 特斯拉工单爬虫项目部署脚本
# 用于在Ubuntu服务器上部署项目环境
#

# 显示执行的命令
set -x

# 错误时退出
set -e

# 项目根目录
PROJECT_ROOT=$(pwd)
echo "项目根目录: $PROJECT_ROOT"

# 确保Python 3.9+ 可用
echo "检查 Python 版本..."
if ! command -v python3.9 &> /dev/null; then
    echo "未找到 Python 3.9，尝试安装..."
    apt-get update
    apt-get install -y software-properties-common
    add-apt-repository -y ppa:deadsnakes/ppa
    apt-get update
    apt-get install -y python3.9 python3.9-venv python3.9-dev
fi

# 验证Python版本
python3.9 --version

# 删除现有虚拟环境（如果存在且不正确）
if [ -e "$PROJECT_ROOT/.venv/bin/activate" ]; then
    source_path=$(grep "VIRTUAL_ENV=" "$PROJECT_ROOT/.venv/bin/activate" | cut -d'=' -f2)
    if [[ "$source_path" != "$PROJECT_ROOT/.venv" && "$source_path" != "\"$PROJECT_ROOT/.venv\"" ]]; then
        echo "检测到错误的虚拟环境路径，删除现有虚拟环境..."
        rm -rf "$PROJECT_ROOT/.venv"
    fi
fi

# 创建虚拟环境
echo "创建 Python 虚拟环境..."
if [ ! -d "$PROJECT_ROOT/.venv" ]; then
    python3.9 -m venv "$PROJECT_ROOT/.venv"
fi

# 验证虚拟环境是否正确
echo "验证虚拟环境..."
if ! grep -q "$PROJECT_ROOT/.venv" "$PROJECT_ROOT/.venv/bin/activate"; then
    echo "虚拟环境路径不正确，重新创建..."
    rm -rf "$PROJECT_ROOT/.venv"
    python3.9 -m venv "$PROJECT_ROOT/.venv"
fi

# 激活虚拟环境
source "$PROJECT_ROOT/.venv/bin/activate"

# 验证虚拟环境是否激活
echo "当前Python路径: $(which python)"
echo "当前虚拟环境: $VIRTUAL_ENV"

# 升级pip
echo "升级 pip..."
python -m pip install --upgrade pip

# 安装依赖
echo "安装项目依赖..."
pip install -r requirements.txt

# 安装playwright及浏览器
echo "安装 Playwright 浏览器..."
pip install playwright
python -m playwright install chromium

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p $PROJECT_ROOT/logs
mkdir -p $PROJECT_ROOT/storage/auth
mkdir -p $PROJECT_ROOT/output
mkdir -p $PROJECT_ROOT/traces

# 设置权限
echo "设置权限..."
chmod +x $PROJECT_ROOT/start.sh
chmod +x $PROJECT_ROOT/main.py

# 配置crontab
echo "配置定时任务..."
(crontab -l 2>/dev/null || echo "") | grep -v "news-spider" | cat - <(echo "0,30 8-18 * * * cd $PROJECT_ROOT && $PROJECT_ROOT/start.sh -e test >> $PROJECT_ROOT/logs/cron.log 2>&1") | crontab -

# 完成
echo "部署完成！"
echo "项目已配置为每天8点到18点，每半个小时执行一次"
echo "可以通过以下命令手动启动项目:"
echo "  cd $PROJECT_ROOT && ./start.sh" 