version: '3.8'

services:
  news-spider:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: news-spider
    restart: unless-stopped
    volumes:
      # 持久化存储
      - ./output:/app/output
      - ./logs:/app/logs
      - ./storage:/app/storage
      - ./conf:/app/conf
    environment:
      - TZ=Asia/Shanghai
      - PLAYWRIGHT_HEADLESS=true
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - news-network

  # 如果需要添加数据库或其他服务，可以在这里定义
  # 示例：PostgreSQL 数据库
  # db:
  #   image: postgres:13
  #   container_name: news-db
  #   restart: unless-stopped
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   environment:
  #     - POSTGRES_DB=news
  #     - POSTGRES_USER=news
  #     - POSTGRES_PASSWORD=securepassword
  #   networks:
  #     - news-network

networks:
  news-network:
    driver: bridge

# volumes:
#   postgres_data: 