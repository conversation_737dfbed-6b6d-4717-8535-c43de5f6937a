#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
News系统API模块
用于与News系统进行交互
"""
from typing import Dict, List, Optional, Union
import time
import concurrent.futures

import requests
from requests.adapters import HTTPAdapter

from core.base import BaseObject
from core.config import ConfigManager
from spiders.tesla.tesla_dao import TeslaOrder, TeslaOrderDAO


class NewsAPI(BaseObject):
    """News系统API，用于与News系统进行交互"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化News系统API
        
        Args:
            config: 配置管理器实例
        """
        super().__init__()
        self.config = config
        
        # 从配置中获取API设置
        self.news_create_url = config.get('news_api.create_url')
        self.timeout = config.get('news_api.timeout', 120)
        self.auth_token = config.get('news_api.auth_token', '')
        self.max_workers = config.get('news_api.max_workers', 5)
        self.batch_size = config.get('news_api.batch_size', 10)
        self.batch_delay = config.get('news_api.batch_delay', 1)  # 批次间延迟，默认1秒
        self.request_delay = config.get('news_api.request_delay', 0.2)  # 请求间延迟，默认0.2秒
        self.dao_manager = TeslaOrderDAO(config)
        
        # 创建带有连接池的 session
        self.session = self._create_session()
        
    def _create_session(self) -> requests.Session:
        """
        创建带有连接池的 requests session
        
        Returns:
            requests.Session: 配置好的session对象
        """
        session = requests.Session()
        
        # 设置适配器，配置连接池大小
        adapter = HTTPAdapter(pool_connections=10, pool_maxsize=20)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
        
    def create_news_order(self, tsl_order: TeslaOrder) -> bool:
        """
        同步工单到News系统
        
        Args:
            tsl_order: 特斯拉工单对象
            
        Returns:
            bool: 创建是否成功
        """
        self.logger.info(f"开始同步工单到News系统: {tsl_order.company_order_no}")
        try:
            payload = {'companyOrderId': tsl_order.company_order_id, 'companyOrderNo': tsl_order.company_order_no,
                       'companyId': tsl_order.company_id, 'templateId': tsl_order.template_id,
                       'custName': tsl_order.cust_name, 'custPhone': tsl_order.cust_phone,
                       'postCode': tsl_order.post_code, 'provinceName': tsl_order.province_name,
                       'cityName': tsl_order.city_name, 'areaName': tsl_order.area_name,
                       'detailAddress': tsl_order.detail_address, 'carBrand': tsl_order.car_brand,
                       'worderType': tsl_order.worder_type, 'worderLevel': tsl_order.worder_level,
                       'dispatchDate': tsl_order.dispatch_date, 'extFields': {
                    "rnNo": tsl_order.rn_no,
                    "orderType": tsl_order.order_type,
                    "planDeliveryDate": tsl_order.plan_delivery_date,
                    "contactInfoRemark": tsl_order.contact_info_remark,
                    "tslCarModel": tsl_order.tsl_car_model,
                    "suiteType": tsl_order.suite_type,
                    "isPreCheck": tsl_order.is_pre_check,
                    "isOfficialPile": tsl_order.is_official_pile
                }}
            # 发送请求
            headers = {"content-Type": "application/json"}
            # 添加认证头信息
            if self.auth_token:
                headers.update({'x-accesstoken': f'{self.auth_token}'})
                
            # 使用会话进行请求，捕获具体的HTTP错误
            try:
                response = self.session.post(self.news_create_url, headers=headers, json=payload, timeout=self.timeout)
                response.raise_for_status()  # 抛出HTTP错误以便更好地处理
            except requests.exceptions.Timeout:
                self.logger.error(f"调用news创建工单接口超时, 车企订单号:{tsl_order.company_order_no}")
                self.dao_manager.update_order_status(
                    tsl_order.company_order_no,
                    status=0,
                    failure_reason="请求超时，请检查网络连接或API服务器状态"
                )
                return False
            except requests.exceptions.ConnectionError:
                self.logger.error(f"调用news创建工单接口连接错误, 车企订单号:{tsl_order.company_order_no}")
                self.dao_manager.update_order_status(
                    tsl_order.company_order_no,
                    status=0,
                    failure_reason="连接错误，请检查网络连接或API服务器地址"
                )
                return False
            except requests.exceptions.HTTPError as http_err:
                status_code = getattr(http_err.response, 'status_code', 0)
                error_text = getattr(http_err.response, 'text', '未知HTTP错误')
                
                self.logger.error(
                    f"调用news创建工单接口HTTP错误, 车企订单号:{tsl_order.company_order_no}, 状态码:{status_code}, 错误:{error_text}")
                
                # 根据不同的HTTP状态码进行不同处理
                if status_code == 401 or status_code == 403:
                    failure_reason = "身份验证失败，请检查认证信息"
                elif status_code == 404:
                    failure_reason = "API地址不存在，请检查接口URL配置"
                elif status_code == 429:
                    failure_reason = "请求过于频繁，请稍后再试"
                elif status_code >= 500:
                    failure_reason = "服务器内部错误，请联系News系统管理员"
                else:
                    failure_reason = f"HTTP错误: {status_code}, {error_text[:200]}"
                
                self.dao_manager.update_order_status(
                    tsl_order.company_order_no,
                    status=0,
                    failure_reason=failure_reason
                )
                return False
            
            # 处理响应结果
            result = response.json()
            if result.get('code') == '0':
                data = result.get('data', {})
                self.logger.info(f"调用news创建工单接口成功,车企订单号:{tsl_order.company_order_no}: 返回数据: {data}")
                tsl_order.worder_no = data.get('worderNo', '')
                tsl_order.status = 1
                tsl_order.failure_reason = ''
                self.dao_manager.update_order(tsl_order)
                return True
            else:
                error_desc = result.get('desc', '未知错误')
                self.logger.error(
                    f"调用news创建工单接口失败,车企订单号:{tsl_order.company_order_no},失败原因: {error_desc}")
                tsl_order.status = 0
                tsl_order.failure_reason = error_desc
                self.dao_manager.update_order_status(
                    tsl_order.company_order_no,
                    status=0,
                    failure_reason=error_desc[:500]  # 截断过长的错误信息
                )
                return False
                
        except Exception as e:
            # 捕获其他请求异常并记录日志
            self.logger.error(f"调用news创建工单接口异常, 车企订单号:{tsl_order.company_order_no},失败原因: {e}")
            self.dao_manager.update_order_status(
                tsl_order.company_order_no,
                status=0,
                failure_reason=str(e)[:500]  # 截断过长的错误信息
            )
            return False
            
    def _process_single_order(self, tsl_order: TeslaOrder) -> bool:
        """
        处理单个订单的同步操作
        
        Args:
            tsl_order: 特斯拉工单对象
            
        Returns:
            bool: 创建是否成功
        """
        # 添加请求前的随机延迟，避免请求过于集中
        if self.request_delay > 0:
            time.sleep(self.request_delay)
        return self.create_news_order(tsl_order)
            
    def batch_create_news_orders(self, orders: List[TeslaOrder]) -> Dict[str, Union[int, List[str]]]:
        """
        批量同步工单到News系统
        
        Args:
            orders: 特斯拉工单对象列表
            
        Returns:
            Dict: 包含成功和失败的订单信息
        """
        if not orders:
            return {"success_count": 0, "total": 0, "failed_orders": []}
            
        self.logger.info(f"开始批量同步工单到News系统, 总数: {len(orders)}")
        
        success_count = 0
        failed_orders = []
        
        # 分批处理订单
        for i in range(0, len(orders), self.batch_size):
            batch = orders[i:i+self.batch_size]
            self.logger.info(f"处理批次 {i//self.batch_size + 1}, 数量: {len(batch)}")
            
            # 并行处理批次内的订单
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任务并获取future对象
                future_to_order = {executor.submit(self._process_single_order, order): order for order in batch}
                
                # 处理完成的任务
                for future in concurrent.futures.as_completed(future_to_order):
                    order = future_to_order[future]
                    try:
                        result = future.result()
                        if result:
                            success_count += 1
                        else:
                            failed_orders.append(order.company_order_no)
                    except Exception as e:
                        self.logger.error(f"并行处理订单异常, 车企订单号:{order.company_order_no}, 错误: {e}")
                        failed_orders.append(order.company_order_no)
            
            # 每批次之间稍作暂停，避免请求过于密集
            if i + self.batch_size < len(orders) and self.batch_delay > 0:
                time.sleep(self.batch_delay)
        
        self.logger.info(f"批量同步工单完成, 成功: {success_count}/{len(orders)}")
        return {
            "success_count": success_count,
            "total": len(orders),
            "failed_orders": failed_orders
        }