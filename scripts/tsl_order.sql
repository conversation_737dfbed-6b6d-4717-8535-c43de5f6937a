/*
 Navicat Premium Dump SQL

 Source Server         : 本地开发
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : test

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 24/03/2025 10:58:08
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tsl_order
-- ----------------------------
DROP TABLE IF EXISTS `tsl_order`;
CREATE TABLE `tsl_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `company_order_id` varchar(50) DEFAULT NULL COMMENT '车企订单id',
  `company_order_no` varchar(50) NOT NULL COMMENT '车企订单号',
  `rn_no` varchar(32) DEFAULT NULL,
  `worder_no` varchar(50) DEFAULT NULL COMMENT 'news订单号',
  `excel_file_name` varchar(100) DEFAULT NULL COMMENT '生成的excel文件名称',
  `status` int DEFAULT NULL COMMENT '工单状态0 初始化 1 已入库 2 已生成基表 3已接收',
  `failure_reason` longtext COMMENT '失败原因',
  `company_id` int NOT NULL COMMENT '车企id',
  `template_id` int NOT NULL COMMENT '模板编号',
  `cust_name` varchar(100) NOT NULL COMMENT '客户姓名',
  `cust_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `post_code` varchar(50) DEFAULT NULL COMMENT '邮箱',
  `province_name` varchar(50) DEFAULT NULL COMMENT '省份名称',
  `city_name` varchar(50) DEFAULT NULL COMMENT '城市名称',
  `area_name` varchar(50) DEFAULT NULL COMMENT '区域代码',
  `detail_address` varchar(200) DEFAULT NULL COMMENT '详细地址',
  `car_brand` varchar(50) DEFAULT NULL COMMENT '车辆品牌',
  `worder_type` varchar(1) NOT NULL COMMENT '工单类型（2-安装,5-勘安,6-维修）',
  `is_pre_check` varchar(1) DEFAULT '否' COMMENT '是否预勘测订单（是/否）',
  `worder_level` varchar(10) DEFAULT 'A' COMMENT '工单级别',
  `customer_order_date` varchar(20) DEFAULT NULL,
  `dispatch_date` varchar(50) DEFAULT NULL COMMENT '车企派单日期',
  `plan_delivery_date` varchar(50) DEFAULT NULL COMMENT '计划提车日期',
  `order_type` varchar(50) DEFAULT NULL COMMENT '订单类型',
  `contact_info_remark` varchar(255) DEFAULT NULL COMMENT '联系信息备注',
  `tsl_car_model` varchar(100) DEFAULT NULL COMMENT '特斯拉车型',
  `suite_type` varchar(50) DEFAULT NULL COMMENT '套包类型',
  `is_official_pile` varchar(1) DEFAULT '否' COMMENT '是否官方移桩服务（是/否）',
  `orign_data` longtext COMMENT '原始数据',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=189 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='特斯拉工单数据';

SET FOREIGN_KEY_CHECKS = 1;
