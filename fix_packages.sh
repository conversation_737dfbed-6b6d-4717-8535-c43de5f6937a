#!/bin/bash
#
# 修复numpy和pandas不兼容问题
#

# 定义变量
PROJECT_ROOT=$(cd "$(dirname "$0")" && pwd)
VENV_PATH="$PROJECT_ROOT/.venv"

# 激活虚拟环境
source "$VENV_PATH/bin/activate"

echo "开始修复numpy和pandas不兼容问题..."

# 卸载现有的numpy和pandas
echo "卸载现有的numpy和pandas..."
pip uninstall -y numpy pandas

# 先安装numpy，指定版本
echo "安装兼容版本的numpy..."
pip install numpy==1.24.4

# 再安装pandas
echo "重新安装pandas..."
pip install pandas

# 检查安装结果
echo "检查安装版本..."
echo "Numpy版本: $(python -c 'import numpy; print(numpy.__version__)')"
echo "Pandas版本: $(python -c 'import pandas; print(pandas.__version__)')"

echo "修复完成，请重新运行start.sh脚本"

# 退出虚拟环境
deactivate 