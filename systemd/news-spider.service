[Unit]
Description=特斯拉工单爬虫服务
After=network.target
Wants=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/opt/news-spider
ExecStart=/opt/news-spider/start.sh
Restart=on-failure
RestartSec=60s
StandardOutput=append:/opt/news-spider/logs/service.log
StandardError=append:/opt/news-spider/logs/service.log
Environment="PYTHONUNBUFFERED=1"
Environment="PLAYWRIGHT_HEADLESS=true"

[Install]
WantedBy=multi-user.target 