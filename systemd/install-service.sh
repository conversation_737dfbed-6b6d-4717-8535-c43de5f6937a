#!/bin/bash
#
# 安装特斯拉爬虫服务到systemd
# 使爬虫能够作为系统服务运行
#

# 显示执行的命令
set -x

# 获取项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)
SERVICE_NAME="news-spider"
SERVICE_FILE="$PROJECT_ROOT/systemd/news-spider.service"
DEST_PATH="/etc/systemd/system/news-spider.service"

# 需要root权限
if [ "$(id -u)" != "0" ]; then
   echo "此脚本需要以root权限运行" 
   exit 1
fi

# 检查服务文件是否存在
if [ ! -f "$SERVICE_FILE" ]; then
    echo "服务文件不存在: $SERVICE_FILE"
    exit 1
fi

# 创建安装目录
INSTALL_DIR="/opt/news-spider"
echo "创建安装目录: $INSTALL_DIR"
mkdir -p $INSTALL_DIR

# 复制项目文件到安装目录
echo "复制项目文件到: $INSTALL_DIR"
rsync -avz --exclude '.git' --exclude '.venv' --exclude '__pycache__' --exclude '*.pyc' $PROJECT_ROOT/ $INSTALL_DIR/

# 设置权限
echo "设置目录权限..."
chown -R www-data:www-data $INSTALL_DIR
chmod +x $INSTALL_DIR/start.sh
chmod +x $INSTALL_DIR/deploy.sh

# 安装服务
echo "安装systemd服务..."
cp $SERVICE_FILE $DEST_PATH
systemctl daemon-reload
systemctl enable $SERVICE_NAME
systemctl restart $SERVICE_NAME
systemctl status $SERVICE_NAME

# 设置定时任务
if [ ! -f "/etc/cron.d/news-spider" ]; then
    echo "配置定时任务..."
    echo "0 * * * * www-data cd $INSTALL_DIR && $INSTALL_DIR/start.sh > /dev/null 2>&1" > /etc/cron.d/news-spider
    chmod 644 /etc/cron.d/news-spider
    echo "定时任务已配置为每小时执行一次"
fi

echo "服务安装完成！"
echo "您可以使用以下命令管理服务:"
echo "  启动服务: systemctl start $SERVICE_NAME"
echo "  停止服务: systemctl stop $SERVICE_NAME"
echo "  查看状态: systemctl status $SERVICE_NAME"
echo "  查看日志: journalctl -u $SERVICE_NAME -f" 