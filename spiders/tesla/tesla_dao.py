#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
特斯拉数据访问模块
处理特斯拉工单系统的数据库操作
"""
import json
import re
from datetime import datetime, date
from typing import Dict, List, Optional

from sqlalchemy import Column, String, Integer, DateTime, BigInteger, func, create_engine
from sqlalchemy.orm import declarative_base, sessionmaker

from core.base import BaseObject
from core.config import ConfigManager

Base = declarative_base()

class TeslaOrder(Base):
    __tablename__ = 'tsl_order'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    company_order_id = Column(String(50), comment='车企订单id')
    company_order_no = Column(String(50), nullable=False, comment='车企订单号')
    rn_no = Column(String(50), nullable=False, comment='RN号')
    worder_no = Column(String(50), comment='news订单号')
    # 文件名称
    excel_file_name = Column(String(100), comment='文件名称')
    # 添加状态字段
    status = Column(Integer, nullable=False, default='0', comment='工单状态0 初始化 1 已入库 2 已生成基表 3已接收')
    # 失败原因
    failure_reason = Column(String(500), comment='失败原因')
    company_id = Column(Integer, nullable=False, comment='车企id')
    template_id = Column(Integer, nullable=False, comment='模板编号')
    cust_name = Column(String(100), nullable=False, comment='客户姓名')
    cust_phone = Column(String(20), nullable=False, comment='客户电话')
    post_code = Column(String(50), comment='邮箱')
    province_name = Column(String(50), comment='省份名称')
    city_name = Column(String(50), comment='城市名称')
    area_name = Column(String(50), comment='区域代码')
    detail_address = Column(String(200), comment='详细地址')
    car_brand = Column(String(50), comment='车辆品牌')
    worder_type = Column(String(1), nullable=False, comment='工单类型（2-安装,5-勘安,6-维修）')
    is_pre_check = Column(String(1), default='否', comment='是否预勘测订单（是/否）')
    worder_level = Column(String(10), default='A', comment='工单级别')
    customer_order_date = Column(String(20), comment='客服收单日期')
    dispatch_date = Column(String(50), comment='车企派单日期')
    # 计划提车日期
    plan_delivery_date = Column(String(50), comment='计划提车日期')
    order_type = Column(String(50), comment='订单类型')
    contact_info_remark = Column(String(255), comment='联系信息备注')
    tsl_car_model = Column(String(100), comment='特斯拉车型')
    suite_type = Column(String(50), comment='套包类型')
    is_official_pile = Column(String(1), default='否', comment='是否官方移桩服务（是/否）')
    orign_data = Column(String, comment='原始数据')
    detail_url = Column(String(255), comment='订单详情链接')
    create_time = Column(DateTime, nullable=False, server_default=func.current_timestamp(), comment='创建时间')
    update_time = Column(DateTime, nullable=False, server_default=func.current_timestamp(), onupdate=func.current_timestamp(), comment='更新时间')


class TeslaOrderDAO(BaseObject):
    def __init__(self, config: ConfigManager):
        # 初始化基类
        super().__init__()
        # 设置连接超时时间为30分钟
        self.engine = create_engine(config.get('database.url'),pool_recycle=1800)
        self.Session = sessionmaker(bind=self.engine)

    def add_order(self, order: TeslaOrder):
        """添加一个新的 TeslaOrder 记录"""
        session = self.Session()
        try:
            # 检查 company_order_no 是否已经存在
            existing_order = session.query(TeslaOrder).filter(TeslaOrder.company_order_no == order.company_order_no).first()
            if existing_order:
                self.logger.warning(f"订单 {order.company_order_no} 已存在.")
                # self.update_order(order)
                return existing_order

            session.add(order)
            session.commit()
            # 刷新对象以获取生成的主键ID
            session.refresh(order)
            return order
        except Exception as e:
            session.rollback()
            self.logger.error(f"Error adding order: {e}")
            return None
        finally:
            session.close()

    def add_orders(self, orders: [TeslaOrder]):
        session = self.Session()
        try:
            session.add_all(orders)
            session.commit()
            return True
        except Exception as e:
            session.rollback()
            self.logger.error(f"Error adding orders: {e}")
            return False
        finally:
            session.close()

    def get_order_by_id(self, order_id: int) -> Optional[TeslaOrder]:
        """根据 ID 获取 TeslaOrder 记录"""
        session = self.Session()
        try:
            return session.query(TeslaOrder).filter(TeslaOrder.id == order_id).first()
        finally:
            session.close()

    def get_order_by_company_order_no(self, company_order_no: str) -> Optional[TeslaOrder]:
        """根据车企订单号获取 TeslaOrder 记录"""
        session = self.Session()
        try:
            return session.query(TeslaOrder).filter(TeslaOrder.company_order_no == company_order_no).first()
        finally:
            session.close()

    def get_orders_by_status(self, status: int) -> Optional[List[TeslaOrder]]:
        """按状态查询订单"""
        session = self.Session()
        try:
            return session.query(TeslaOrder).filter(TeslaOrder.status == status).all() or None
        except Exception as e:
            self.logger.error(f"订单查询失败: {str(e)}")
            return None
        finally:
            session.close()

    def update_order(self, order: TeslaOrder) -> Optional[bool]:
        """更新 TeslaOrder 记录"""
        session = self.Session()
        try:
            session.merge(order)
            session.commit()
            return True
        except Exception as e:
            session.rollback()
            self.logger.error(f"Error updating order: {e}")
            return False
        finally:
            session.close()

    def update_order_status(self, company_order_no: str, **kwargs):
        """通用状态更新方法"""
        session = self.Session()
        try:
            order = session.query(TeslaOrder).filter(TeslaOrder.company_order_no == company_order_no).first()

            if order:
                for key, value in kwargs.items():
                    if hasattr(order, key):
                        setattr(order, key, value)
                session.commit()
                return True
            return False
        except Exception as e:
            session.rollback()
            self.logger.error(f"状态更新失败: {company_order_no} | 错误: {str(e)}")
            return False
        finally:
            session.close()

    def delete_order(self, order: TeslaOrder) -> Optional[bool]:
        """删除 TeslaOrder 记录"""
        session = self.Session()
        try:
            session.delete(order)
            session.commit()
            return True
        except Exception as e:
            session.rollback()
            self.logger.error(f"Error deleting order: {e}")
            return False
        finally:
            session.close()

    def get_all_orders(self) -> Optional[list]:
        """获取所有 TeslaOrder 记录"""
        session = self.Session()
        try:
            return session.query(TeslaOrder).all()
        finally:
            session.close()

    def build_orders(self, items: List[Dict]) -> Optional[List[TeslaOrder]]:
        # items转换成orders
        orders = []
        for item in items:
            dispatch_date = datetime.strptime(item.get('SharedDateString'), "%m/%d/%Y").strftime("%Y-%m-%d") if item.get('SharedDateString') else ''
            # 获取 province_name 并处理
            province_name = item.get('InstallationState', '')
            if province_name:
                # 基础清洗：去除空格和特殊字符
                province_name = province_name.strip().replace('　', '')

                # 替换省级后缀
                province_name = province_name.replace('省', '')

                # 处理自治区
                autonomous_mapping = {
                    '内蒙古自治区': '内蒙古',
                    '广西壮族自治区': '广西',
                    '西藏自治区': '西藏',
                    '宁夏回族自治区': '宁夏',
                    '新疆维吾尔自治区': '新疆'
                }
                # 使用正则匹配带括号的完整名称
                for full_name, short_name in autonomous_mapping.items():
                    if re.match(fr'^{re.escape(full_name)}', province_name):
                        province_name = short_name
                        break

                # 最后处理可能的残留字符
                province_name = province_name.replace('自治区', '').replace('特别行政区', '')
            else:
                province_name = ''
            city_name = item.get('InstallationCity').replace('市', '') if item.get('InstallationCity') else ''
            # 定义一个正则表达式模式来匹配区县信息
            match = re.match(r'^(.+?[区县市])', item.get('InstallationStreet').split(' ')[0]) if  item.get('InstallationStreet') else ''
            area_name = match.group(1) if match else ''

            template_id = item.get('TemplateId')
            worder_type = '5'
            if template_id == 246 or template_id == 247:
                worder_type = '6'
            order = TeslaOrder(
                company_order_id=item.get('Name'),
                company_order_no=item.get('Name'),
                rn_no=item.get('SoName'),
                customer_order_date=date.today().strftime('%Y-%m-%d'),
                dispatch_date=dispatch_date,
                plan_delivery_date=item.get('ScheduledDeliveryDate'),
                status=0,
                company_id=289,
                template_id=item.get('TemplateId'),
                cust_name=item.get('Account', {}).get('FirstName', ''),
                cust_phone=item.get('Account', {}).get('SMPPhone', ''),
                post_code=item.get('Account', {}).get('SMPEmail', '')[:50],
                province_name=province_name,
                city_name=city_name,
                area_name=area_name,
                detail_address=item.get('InstallationStreet'),
                car_brand='14',
                worder_type=worder_type,
                worder_level='A',
                # 是否预勘测订单
                is_pre_check=item.get('IsPreCheck'),
                # 选配订单,天猫订单,电商平台订单
                order_type=item.get('OrderType'),
                contact_info_remark=item.get('remark'),
                tsl_car_model=item.get('TslCarModel'),
                suite_type=item.get('SuiteType'),
                is_official_pile=item.get('IsOfficialPile'),
                orign_data=json.dumps(item),
                detail_url=item.get('DetailUrl')
            )
            orders.append(order)
        return orders