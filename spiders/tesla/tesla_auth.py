#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
特斯拉认证模块
处理特斯拉工单系统的认证
"""
import json
import random
import re
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Optional
from urllib.parse import urlparse

from playwright.sync_api import Page, BrowserContext

from core.config import ConfigManager
from core.utils.browser import BrowserManager
from core.utils.human import HumanBehaviorSimulator
from spiders.base.auth import BaseAuth
from spiders.base.exception import BrowserError


class TeslaAuth(BaseAuth):
    """特斯拉认证管理器，处理特斯拉工单系统的登录和认证"""
    
    MODULE_NAME = 'tesla'
    
    def __init__(self, config: ConfigManager):
        """
        初始化特斯拉认证管理器
        
        Args:
            config: 配置管理器实例
        """
        super().__init__(config)
        
        # 认证信息存储路径
        auth_path = self._get_config('login.storage_state_path')
        self.set_auth_file(auth_path)
        
        # 登录配置
        self.login_url = self._get_config('login.url')
        self.username = self._get_config('login.username')
        self.password = self._get_config('login.password')
        self.max_login_retries = self._get_config('login.max_login_retries', 3)
        
        # 浏览器管理器
        self.browser_manager = BrowserManager(config)
        
        # 人类行为模拟器
        self.human = HumanBehaviorSimulator()
        # 鼠标初始位置
        self.current_mouse = (100, 200)
        
    def login(self) -> Optional[Dict]:
        """
        执行登录过程，获取认证信息
        
        Returns:
            Optional[Dict]: 认证信息字典，如果登录失败则返回None
        """
        self.logger.info("开始特斯拉工单系统登录")
        """执行登录流程"""
        try:
            """更新认证信息"""
            self.logger.info("🔄 正在更新认证信息...")
            with BrowserManager(self.config) as context:
                page = context.new_page()
                page.add_init_script("""
                                    window.chrome = { runtime: {} };
                                    Object.defineProperty(navigator, 'plugins', {
                                    get: () => {
                                        function FakePluginArray() {}
                                        FakePluginArray.prototype = Object.create(Array.prototype);
                                        Object.defineProperty(FakePluginArray.prototype, 'item', {
                                        value: function(index) { return this[index]; }
                                        });
                                        Object.defineProperty(FakePluginArray.prototype, 'namedItem', {
                                        value: function(name) { return this.find(p => p.name === name); }
                                        });
                                        return new FakePluginArray(1, 2, 3, 4, 5);
                                    }
                                    });
                                    Object.defineProperty(navigator, 'languages', {
                                    get: () => ['zh-CN', 'zh']
                                    });
                                    const getParameter = WebGLRenderingContext.prototype.getParameter;
                                    WebGLRenderingContext.prototype.getParameter = function(parameter) {
                                    if (parameter === 37445) return 'Intel Inc.';
                                    if (parameter === 37446) return 'Intel Iris OpenGL Engine';
                                    return getParameter(parameter);
                                    };
                                    const originalQuery = window.navigator.permissions.query;
                                    window.navigator.permissions.query = (parameters) => (
                                    parameters.name === 'notifications'
                                        ? Promise.resolve({ state: Notification.permission })
                                        : originalQuery(parameters)
                                    );
                                    const getContext = HTMLCanvasElement.prototype.getContext;
                                    HTMLCanvasElement.prototype.getContext = function(type, attrs) {
                                    if (type === 'webgl' || type === 'experimental-webgl') {
                                        return getContext.call(this, type, attrs) || {};
                                    }
                                    return getContext.call(this, type, attrs);
                                    };
                                """)
                # page.goto("https://bot.sannysoft.com/")
                # page.wait_for_load_state('networkidle', timeout=20000)
                # raise Exception("加载完成")
                self._perform_login(page)
                HumanBehaviorSimulator.random_delay(1000, 20000)
                self._save_session(context)
                return self.load_auth_info()
        except Exception as e:
            if self.browser_manager.enable_tracing:
                trace_path = "traces/error_trace.zip"
                context.tracing.stop(path=trace_path)
            raise Exception(f"更新认证信息失败,登录失败: {str(e)}") from e
            
    def _perform_login(self, page: Page) -> Optional[Dict]:
        """
        执行具体的登录操作
        
        Args:
            page: Playwright页面对象
            
        Returns:
            Optional[Dict]: 认证信息字典，如果登录失败则返回None
        """
        try:
            """导航到登录页"""
            page.goto(f"{self.login_url}?r={random.randint(1000, 9999)}")
            self.human.random_scroll(page)
            self.human.random_delay(1000, 2000)

            """处理用户名输入"""
            username_field = page.wait_for_selector("#form-input-identity", timeout=10000)
            self.current_mouse = self.human.realistic_click(page, username_field, self.current_mouse)
            self.human.realistic_type(username_field, self.username)

            """点击下一步|Next"""
            next_btn = page.get_by_role("button", name=re.compile(r"下一步|Next"))
            self.current_mouse = self.human.realistic_click(page, next_btn, self.current_mouse)

            """处理密码输入"""
            password_field = page.wait_for_selector("#form-input-credential", timeout=10000)
            if not password_field:
                raise Exception("未找到密码输入框")

            self.current_mouse = self.human.realistic_click(page, password_field, self.current_mouse)
            self.human.realistic_type(password_field, self.password)

            login_btn = page.get_by_role("button", name=re.compile(r"登录|Sign In"))
            self.current_mouse = self.human.realistic_click(page, login_btn, self.current_mouse)
            
            # 等待登录完成
            self.logger.info("等待登录完成")
            
            # 检查是否登录成功
            try:
                # 等待重定向或者Dashboard页面加载
                page.wait_for_load_state('networkidle', timeout=20000)
                
                # 检查页面URL或特定元素，判断是否登录成功
                self.logger.info(f"当前页面URL: {page.url}")

                self.human.random_scroll(page)
                self.human.random_delay(2000, 10000)
                    
                # 登录成功，获取登录信息
                self.logger.info("登录成功")
                
                # 构建认证信息
                auth_info = {
                    'username': self.username,
                    'session_id': '',  # 可能需要从cookies中提取
                    'login_time': datetime.now().isoformat(),
                    'expire_time': (datetime.now() + timedelta(days=1)).isoformat()
                }
                
                return auth_info
            except Exception as e:
                self.logger.error(f"等待登录完成时发生异常: {str(e)}")
                
                # 如果页面上有验证码或其他安全挑战，可能需要特殊处理
                if page.is_visible('#captcha') or page.is_visible('#security-challenge'):
                    self.logger.warning("检测到安全验证，需要人工干预")
                    
                return None
        except Exception as e:
            self.logger.error(f"登录操作失败: {str(e)}")
            return None
            
    def is_valid(self, auth_info: Optional[Dict]) -> bool:
        """
        检查认证信息是否有效
        
        Args:
            auth_info: 认证信息字典
            
        Returns:
            bool: 认证信息是否有效
        """
        if not auth_info:
            return False

        """验证认证有效性"""
        return datetime.now() < auth_info.get('expires_at', datetime.min) - timedelta(hours=1)
        # and self.api_client.verify_token(auth_info)
            
    def refresh(self, auth_info: Optional[Dict]) -> Optional[Dict]:
        """
        刷新认证信息
        
        Args:
            auth_info: 原认证信息字典
            
        Returns:
            Optional[Dict]: 刷新后的认证信息字典，如果刷新失败则返回None
        """
        # 特斯拉不支持直接刷新令牌，需要重新登录
        self.logger.info("特斯拉认证不支持直接刷新，需要重新登录")
        return None
    def load_auth_info(self) -> Dict:
        """加载认证信息"""
        try:
            with open(self.auth_file, 'r') as f:
                state_data = json.load(f)
                target_domain = urlparse("https://partners.tesla.cn").netloc
                for origin in state_data.get('origins', []):
                    current_domain = urlparse(origin['origin']).netloc
                    if current_domain == target_domain:
                        storage =  {item['name']: item['value'] for item in origin.get('localStorage', [])}
                        """解析认证数据"""
                        auth_json = storage.get('partner-leadsharing:token', '{}')
                        auth_data = json.loads(auth_json) or {}

                        issued_at = datetime.fromtimestamp(auth_data.get('issuedAt', 0))
                        return {
                            'access_token': auth_data.get('accessToken', ''),
                            'token_type': auth_data.get('tokenType', ''),
                            'expires_in': auth_data.get('expiresIn', 0),
                            'issued_at': issued_at,
                            'expires_at': issued_at + timedelta(seconds=auth_data.get('expiresIn', 0)),
                            'id_token': auth_data.get('idToken', ''),
                            'raw_data': auth_data
                        }
            return {}
        except FileNotFoundError:
            self.logger.error("本地认证信息文件不存在")
            return {}
        except json.JSONDecodeError:
            self.logger.error("本地存储文件解析失败")
            return {}
        except Exception as e:
            self.logger.error(f"本地认证信息数据提取异常: {str(e)}")
            return {}

    def show_auth_info(self,auth_info: Dict) -> None:
        """显示认证信息"""
        self.logger.info("🔐 认证信息摘要：")
        self.logger.info(f"• 令牌类型: {auth_info['token_type']}")
        self.logger.info(f"• 访问令牌: {auth_info['access_token']}...")
        self.logger.info(f"• ID令牌: {auth_info['id_token']}...")
        self.logger.info(f"• 签发时间: {auth_info['issued_at'].strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info(f"• 有效时长: {auth_info['expires_in'] // 3600} 小时")
        self.logger.info(f"• 过期时间: {auth_info['expires_at'].strftime('%Y-%m-%d %H:%M:%S')}")

    def _save_session(self, context: BrowserContext) -> None:
        """保存会话状态"""
        try:
            context.storage_state(path=self.auth_file)
            self.logger.info("✅ 浏览器状态已保存")
        except Exception as e:
            raise BrowserError(f"状态保存失败: {str(e)}")
