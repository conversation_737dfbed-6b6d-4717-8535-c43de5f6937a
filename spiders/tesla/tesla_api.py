#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
特斯拉API模块
处理特斯拉工单系统的API调用
"""
from typing import Dict, List, Optional

import requests

from core.base import BaseObject
from core.config import ConfigManager
from core.notifier.feishu import FeishuNotifier
from core.utils.human import HumanBehaviorSimulator
from spiders.base.exception import NetworkError
from spiders.tesla.tesla_dao import TeslaOrder


class TeslaAPI(BaseObject):
    """特斯拉API客户端，处理特斯拉工单系统的API调用"""
    
    MODULE_NAME = 'tesla'
    
    def __init__(self, config: ConfigManager):
        """
        初始化特斯拉API客户端
        
        Args:
            config: 配置管理器实例
        """
        super().__init__(config)
        
        # API配置
        self.page_size = self._get_config('tesla_pp.fetch.page_size')
        self.order_list_url = self._get_config('tesla_pp.order_list_url')
        self.order_detail_url = self._get_config('tesla_pp.order_detail_url')
        self.order_status_url = self._get_config('tesla_pp.order_status_url')
        
        # 请求配置
        self.timeout = self._get_config('tesla_pp.fetch.timeout', 60)
        
        # 人类行为模拟器
        self.human = HumanBehaviorSimulator()

        self.feishu_notifier = FeishuNotifier(config)
        
    def fetch_orders(self, auth_info: Dict, order_type: str = 'smp') -> List[Dict]:
        """
        获取工单列表
        
        Args:
            auth_info: 认证信息
            order_type: 工单类型
        Returns:
            List[Dict]: 工单列表

        """
        # 确保logger.propagate设置为False
        # self.logger.propagate = False
        self.logger.info(f"开始获取{order_type}类型工单")
        
        try:
            # 添加人类行为随机延迟
            self.human.random_delay()
            response = requests.post(
                self.order_list_url,
                params={"pageIndex": "0","pageSize": "200"},
                headers=self._build_headers(auth_info),
                json = self._build_order_payload({"Source": order_type}),
                timeout=self.timeout
            )

            if response.status_code != 200:
                raise NetworkError(f"工单请求失败: {response.status_code}")

            items = response.json().get("Items", [])
            # 解析响应数据
            self.logger.info(f"工单列表获取完成，共获取{len(items)}条{order_type}类型工单")
            return items
        except Exception as e:
            self.logger.error(f"获取{order_type}类型工单列表时发生异常: {str(e)}")
            return []
            
    def fetch_order_detail(self, auth_info: Dict, item):
        """
        获取工单详情

        Args:
            auth_info: 认证信息
            item: 工单信息
        Returns:
            Dict: 工单详情
        """
        self.logger.info(f"开始获取工单详情: {item.get('Name')}")
        item_id = item.get("Id")
        account_id = item.get("AccountId")
        if item_id and account_id:
            headers = self._build_headers(auth_info)
            url = f"{self.order_detail_url}/{item_id}/{account_id}"
            HumanBehaviorSimulator.random_delay()
            response = requests.get(url, headers=headers, timeout=self.timeout)
            if response.status_code == 200:
                data = response.json()
                bill_of_materials = data.get("BillOfMaterials", [])
                vehicle_information = data.get("VehicleInformation", {})
                # 如果bill_of_materials不为空，则遍历bill_of_materials，获取第一个Description，返回字符串
                if bill_of_materials:
                    first_item = bill_of_materials[0]
                    description = first_item.get("Description")
                    if description:
                        item["SuiteType"] = description
                        self.logger.info(f"套包类型: {description}")
                if vehicle_information:
                    item['TslCarModel'] = vehicle_information.get("Model")
            else:
                print(f"Failed to retrieve data: {response.status_code}")
        return item

    def accept_orders(self, auth_info: Dict, orders: List[TeslaOrder]):
        """接受工单并返回成功列表"""
        successful_orders = []

        for order in orders:
            # 如果company_order_no包含"【拆】"，则跳过该订单
            if "【拆】" in order.company_order_no:
                continue
            try:
                HumanBehaviorSimulator.random_delay(1000,2000)
                response = requests.put(self.order_status_url, headers=self._build_headers(auth_info), data=order.orign_data)
                if response.status_code == 200:
                    successful_orders.append(order)
                    self.logger.debug(f"✅ 工单接受成功: {order.company_order_no}")
                else:
                    error_msg = f"工单接受失败[{response.status_code}]: {order.company_order_no}"
                    self.logger.error(error_msg)
                    # self.feishu_notifier.send_alert("工单接受","",error_msg)

            except Exception as e:
                error_msg = f"工单接受异常: {order.company_order_no} | 错误: {str(e)}"
                self.logger.error(error_msg, exc_info=True)
                self.feishu_notifier.send_alert("工单接受","",error_msg)
        total = len(successful_orders)
        self.logger.info(f"📥 工单接受完成，成功 {total}/{len(orders)} 条")
        return successful_orders
        
    def _build_headers(self, auth_info: Dict) -> Dict:
        """
        构建API请求头
        
        Args:
            auth_info: 认证信息
            
        Returns:
            Dict: 请求头字典
        """
        headers = {
            "Host": "akamai-apigateway-partnerportalcn.tesla.cn",
            "Connection": "keep-alive",
            "sec-ch-ua-platform": "\"windows\"",
            "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"",
            "sec-ch-ua-mobile": "?0",
            "Accept": "application/json",
            "channel": "web",
            "account-country": "CN",
            "Origin": "https://partners.tesla.cn",
            "Sec-Fetch-Site": "same-site",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://partners.tesla.cn/",
            "Accept-Language": "zh-CN,zh;q=0.9"
        }
        
        # 添加认证信息
        if "access_token" in auth_info:
            headers["Authorization"] = f"Bearer {auth_info['access_token']}"
        
        if "id_token" in auth_info:
            headers["idtoken"] = auth_info["id_token"]
            
        # 添加User-Agent
        user_agent = self.config.get('Browser.user_agent')
        if user_agent:
            headers["User-Agent"] = user_agent
            
        # 添加Content-Type
        headers["Content-Type"] = "application/json"
        
        return headers

    @staticmethod
    def _build_order_payload(overrides: Optional[Dict] = None) -> Dict:
        """构建工单请求负载"""
        payload = {
            "FirstName": None,
            "LastName": None,
            "Name": None,
            "InstallationPostalCode": None,
            "InstallationCity": None,
            "InstallationCountry": None,
            "InstallationRegion": None,
            "BidStatus": None,
            "InstallationStatus": None,
            "Status": None,
            "Tab": "New",
            "SoProduct": None,
            "SortActive": "id",
            "SortDirection": "desc",
            "State": None,
            "QuoteExpirationDateRange": None,
            "SharedDateRange": None,
            "ScheduledDeliveryDateRange": None,
            "Rn": None,
            "AccountId": None,
            "Source": "smp",
            "FoStatus": None,
            "CustomerPhoneNumber": None,
            "CustomerEmail": None,
            "InitialContactDateRange": None,
            "SiteVisitDateRange": None,
            "InstallationDateRange": None,
            "LastUpdateDateRange": None,
            "OrderNumber": None,
            "ShippingDateRange": None,
            "ShippingTrackingNumber": None,
            "SharedStartDate": None,
            "SharedEndDate": None
        }
        if overrides:
            # 过滤无效字段，仅允许覆盖预定义键
            valid_keys = payload.keys()
            filtered_updates = {k: v for k, v in overrides.items() if k in valid_keys}
            payload.update(filtered_updates)
        return payload