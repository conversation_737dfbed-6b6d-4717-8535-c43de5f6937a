#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
特斯拉爬虫模块
爬取特斯拉工单系统数据
"""
import copy
import os
import re
import smtplib
from datetime import datetime
from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import Dict, List, Optional

import openpyxl
import pandas as pd
from openpyxl.styles import Font

from api.news_api import NewsAPI
from core.config import ConfigManager
from spiders.base.exception import TeslaError
from spiders.base.spider import BaseSpider
from spiders.tesla.tesla_api import TeslaAPI
from spiders.tesla.tesla_auth import TeslaAuth
from spiders.tesla.tesla_dao import TeslaOrderDAO, TeslaOrder


class TeslaSpider(BaseSpider):
    """特斯拉爬虫，爬取特斯拉工单系统数据"""
    
    MODULE_NAME = 'tesla'
    
    def __init__(self, config: ConfigManager):
        """
        初始化特斯拉爬虫
        
        Args:
            config: 配置管理器实例
        """
        super().__init__(config)
        
        # 初始化组件
        self.auth_manager = TeslaAuth(config)
        self.dao_manager = TeslaOrderDAO(config)
        self.api_manager = TeslaAPI(config)
        self.news_manager = NewsAPI(config)
        self.notify_manager = None  # 禁用通知功能
        
        # API配置
        self.news_api_create_url = self._get_config('news_api.create_url')
        # 邮箱
        self.email = self._get_config('email', {})
        # 报表配置
        self.report_output = self._get_config('report.output_file')

    def run(self) -> bool:
        """
        执行爬虫任务

        Returns:
            bool: 执行是否成功
        """
        try:
            self.logger.info("开始执行特斯拉爬虫任务")

            # 阶段1：获取认证信息
            self.logger.info("🚀 阶段1:认证信息获取开始...")
            auth_info = self.auth()
            if not auth_info:
                self.logger.error("⚠️ 阶段1:认证信息获取失败，请检查配置文件或网络连接")
                return False
            self.auth_manager.show_auth_info(auth_info)
            self.logger.info("🚀 阶段1:认证信息获取成功...")

            # 阶段2：数据抓取
            self.logger.info("🚀 阶段2:数据抓取开始...")
            processed_data = self.fetch_data(auth_info)
            if not processed_data:
                self.logger.warning("没有获取到任何工单数据")
            self.logger.info("🚀 阶段2:数据抓取完成...")

            # 阶段3：订单保存
            self.logger.info("🚀 阶段3:订单保存开始...")
            saved_orders = self.save_data(processed_data)
            self.logger.info("🚀 阶段3:订单保存完成...")

            # 阶段4：订单接受
            # 如果当前是生产环境，执行订单接受操作
            if self.config.get_env() == 'prod':
                self.logger.info("🔧 阶段4:工单接受开始...")
                self.api_manager.accept_orders(auth_info, saved_orders)
                self.logger.info("🔧 阶段4:工单接受结束...")
            else:
                self.logger.info("🔧 阶段4:工单接受跳过，当前环境非生产环境")

            # 阶段5：订单创建
            self.logger.info("🔧 阶段5:工单创建开始...")
            self.sync_to_news()
            self.logger.info("🔧 阶段5:工单创建结束...")

            # 阶段6：生成Excel
            self.logger.info("📊 阶段6:Excel生成开始...")
            excel_file_name = self.generate_report()
            self.logger.info("📊 阶段6:Excel生成完成...")

            # 阶段7：邮件发送
            self.logger.info("📨 阶段7:邮件发送开始...")
            if excel_file_name:
                self._send_excel_email(excel_file_name)
                self.logger.info("📨 阶段7:邮件发送完成...")
            else:
                self.logger.warning("⚠️ 无法发送邮件，因为文件不存在")

            self.logger.info("🎉 数据流水线执行完毕！")

            self.logger.info("特斯拉爬虫任务执行成功")
            return True

        except Exception as e:
            self.logger.error(f"执行特斯拉爬虫任务时发生异常: {str(e)}", exc_info=True)
            return False

    def auth(self) -> Dict:
        """
        登录并获取认证信息
        
        Returns:
            Dict: 认证信息字典
        """
        self.logger.info("开始登录特斯拉工单系统")
        
        # 尝试加载现有认证信息
        auth_info = self.auth_manager.load_auth_info()
        
        # 检查认证信息是否有效
        if auth_info and self.auth_manager.is_valid(auth_info):
            self.logger.info("使用现有有效的认证信息")
            return auth_info
            
        # 尝试刷新认证信息
        if auth_info:
            self.logger.info("尝试刷新认证信息")
            refreshed_auth = self.auth_manager.refresh(auth_info)
            if refreshed_auth:
                self.logger.info("成功刷新认证信息")
                return refreshed_auth
                
        # 执行完整登录流程
        self.logger.info("执行完整登录流程")
        new_auth = self.auth_manager.login()
        
        if not new_auth:
            self.logger.error("登录失败")
            raise Exception("登录失败")
            
        self.logger.info("登录成功")
        return new_auth

    def fetch_data(self, auth_info: Dict) -> List[Dict]:
        """
        根据认证信息获取数据
        
        Args:
            auth_info: 认证信息
            
        Returns:
            List[Dict]: 获取到的数据列表
        """
        self.logger.info("开始获取工单数据")
        # 获取工单列表
        smp_items = self.api_manager.fetch_orders(
            auth_info=auth_info,
            order_type="smp"
        )

        self.logger.info(f"成功获取{len(smp_items)}条选配订单数据")
        for item in smp_items:
            item['IsPreCheck'] = '是'
            item['OrderType'] = '选配订单'
            item['IsOfficialPile'] = '否'
            item['TemplateId'] = 381

        grid_items = self.api_manager.fetch_orders(
            auth_info=auth_info,
            order_type="grid"
        )
        self.logger.info(f"成功获取{len(grid_items)}条电商平台订单数据")


        # 移桩订单
        yzItems = []
        for item in grid_items:
            item['IsPreCheck'] = '否'
            item['OrderType'] = '电商平台订单'
            self.logger.info(f"开始获取订单{item.get('Name')}详情数据")
            item = self.api_manager.fetch_order_detail(auth_info, item)
            # 套包类型
            suite_type = item.get("SuiteType")
            template_id = 709
            if suite_type:
                # 匹配40米或40 米
                if re.search(r'40\s*米', suite_type.lower()):
                    if "cybervault" in suite_type.lower():
                        template_id = 745
                    else:
                        template_id = 799

                elif any(keyword in suite_type.lower() for keyword in ["0米", "含勘测服务"]):
                    province_name = item.get("InstallationState", '')
                    if "新疆" in province_name.lower():
                        template_id = 731
                    elif any(keyword in province_name.lower() for keyword in ["云南", "甘肃", "宁夏"]):
                        template_id = 730
                    elif any(keyword in province_name.lower() for keyword in ["上海", "江苏", "浙江"]):
                        template_id = 703
                    else:
                        template_id = 709
                elif "家庭充电桩移桩服务" in suite_type.lower():
                    item['IsOfficialPile'] = '是'
                    template_id = 246
                    yzItem = copy.deepcopy(item)
                    yzItems.append(yzItem)
            item['TemplateId'] = template_id

        self.logger.info(f"开始处理{len(yzItems)}条移桩订单")
        for yzItem in yzItems:
            # 修改yzItem中的templateId为247，并将isOfficialPile设置为'是'，并将Name改为Name+【拆】
            yzItem['TemplateId'] = 247
            yzItem['Name'] = yzItem['Name'] + '【拆】'
            grid_items.append(yzItem)


        items = smp_items + grid_items
        if not items:
            self.logger.warning("没有获取到任何工单数据")
            return []

        for item in items:
            item_id = item.get("Id")
            account_id = item.get("AccountId")
            # 如果 item['IsPreCheck'] = '是',则在地址中添加?type=presale
            if item.get("IsPreCheck") == '是':
                detail_url = f"https://partners.tesla.cn/home/<USER>/{item_id}/{account_id}?type=presale"
            else:
                detail_url = f"https://partners.tesla.cn/home/<USER>/{item_id}/{account_id}"
            item["DetailUrl"] = detail_url

            notes = item.get("Notes", [])
            # 遍历notes，获取note中的Note，并过滤掉不包含中文的Note，返回拼接字符串,并现在字符串最大长度255
            notes_str = "\n".join(
                f"{note.get('Note')}"
                for note in notes
                # 增加中文校验条件：使用正则表达式匹配中文字符
                if not any(kw in note.get("Note", "") for kw in {"预勘测订单创建", "到每家科技", "->"})
                and re.search(r'[\u4e00-\u9fff]', note.get("Note", ""))
            )
            item["remark"] = notes_str[:255]
            
        self.logger.info(f"成功获取{len(items)}条工单数据")
        return items

    def process_data(self, items: List[Dict]) -> List[Dict]:
        """
        处理获取到的数据
        
        Args:
            items: 获取到的原始数据
            
        Returns:
            List[Dict]: 处理后的数据
        """
        return items

    def save_data(self, items: List[Dict]) -> List[TeslaOrder]:
        self.logger.info(f"开始保存{len(items)}条工单数据")

        """批量保存并返回成功订单列表"""
        success_orders = []
        orders = self.dao_manager.build_orders(items)
        for order in orders:
            order_no = getattr(order, 'company_order_no', '未知订单号')
            try:
                saved_order = self.dao_manager.add_order(order)
                if saved_order:
                    success_orders.append(saved_order)  # 保存完整的订单对象
                    self.logger.debug(f"✅ 订单保存成功: {order_no}")
                else:
                    self.logger.warning(f"订单保存返回空: {order_no}")
            except Exception as e:
                error_msg = f"订单保存失败: {order_no} | 错误: {str(e)}"
                self.logger.error(error_msg)

        self.logger.info(f"📦 订单保存完成，成功 {len(success_orders)}/{len(items)} 条")
        return success_orders  # 返回成功订单列表
        
    def sync_to_news(self):
        """阶段3：处理待创建工单"""
        self.logger.info("🔧 开始同步news工单阶段...")

        pending_orders = self.dao_manager.get_orders_by_status(0)
        if not pending_orders:
            self.logger.info("✅ 没有待同步news的工单")
            return
        
        # 使用批量处理接口替代逐个处理
        try:
            result = self.news_manager.batch_create_news_orders(pending_orders)
            success_count = result["success_count"]
            failed_orders = result["failed_orders"]
            
            if failed_orders:
                self.logger.warning(f"有 {len(failed_orders)} 个工单同步失败: {', '.join(failed_orders[:10])}{'...' if len(failed_orders) > 10 else ''}")
            
        except Exception as e:
            self.logger.error(f"批量同步news工单过程中发生异常: {str(e)}")
            success_count = 0

        self.logger.info(f"✅ 同步news工单完成，成功 {success_count}/{len(pending_orders)} 条")

    def generate_report(self) -> Optional[str]:
        """
        生成工单报表
        
        Returns:
            Optional[str]: 报表文件路径，如果生成失败则返回None
        """
        self.logger.info("开始生成工单报表")
        
        try:
            # 获取全部工单数据
            orders = self.dao_manager.get_orders_by_status(1)
            
            if not orders:
                self.logger.warning("没有工单数据，无法生成报表")
                return None
                
            self.logger.info(f"获取到{len(orders)}条工单数据用于生成报表")

            template_columns = [
                '客服收单日期', '车企派单日期', '安装项目编号', '单独RN号',
                '用户姓名', '联系电话', '地址', '套包类型', '省', '市', '区县', '金牌订单编号', '联系信息备注'
            ]

            excel_datas = []
            # 遍历 items 并填充 DataFrame
            for order in orders:
                excel_data = {
                    # 当前日期
                    '客服收单日期': order.customer_order_date,
                    '车企派单日期': order.dispatch_date,
                    '安装项目编号': order.company_order_no,
                    '单独RN号': order.rn_no,
                    '用户姓名': order.cust_name,
                    '联系电话': order.cust_phone,
                    '地址': f"{order.province_name}{order.city_name}{order.area_name}{order.detail_address}",
                    '套包类型': order.suite_type,
                    '省': order.province_name,
                    '市': order.city_name,
                    '区县': order.area_name,
                    '金牌订单编号': order.worder_no,
                    '联系信息备注': order.contact_info_remark
                }
                excel_datas.append(excel_data)
            # 创建一个DataFrame，使用模板列名
            df = pd.DataFrame(columns=template_columns, data=excel_datas)
            
            # 设置报表输出路径
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M')
            file_name = f"tesla_report_{current_time}.xlsx"
            output_dir = self.report_output if self.report_output else 'output'
            os.makedirs(output_dir, exist_ok=True)
            output_file = os.path.join(output_dir, file_name)
            # 导出到Excel
            with pd.ExcelWriter(output_file) as writer:
                df.to_excel(writer, sheet_name='特斯拉工单', index=False)
                worksheet = writer.sheets['特斯拉工单']
                
                # 为安装项目编号列添加超链接
                # 找到安装项目编号列的索引
                project_col_idx = template_columns.index('安装项目编号') + 1  # +1是因为Excel列从1开始
                
                # 遍历每一行，添加超链接
                hyperlink_count = 0
                for row_idx, order in enumerate(orders, start=2):  # Excel行从2开始（跳过表头）
                    if hasattr(order, 'detail_url') and order.detail_url:
                        cell = worksheet.cell(row_idx, project_col_idx)
                        # 获取当前单元格文本
                        cell_text = cell.value
                        # 设置超链接和样式
                        cell.hyperlink = order.detail_url
                        # 保持文本显示为单元格原始值
                        cell.value = cell_text
                        # 设置样式为蓝色和下划线
                        cell.font = openpyxl.styles.Font(color="0000FF", underline="single")
                        hyperlink_count += 1
                
                self.logger.info(f"已为 {hyperlink_count}/{len(orders)} 个订单添加超链接")

            success_count = 0
            for order in orders:
                self.dao_manager.update_order_status(
                    order.company_order_no,
                    status=2,
                    excel_file_name=file_name
                )
                success_count += 1
            self.logger.info(f"✅ 报表生成成功: {file_name}，成功处理 {success_count} 条数据")
            return file_name
        except Exception as e:
            self.logger.error(f"生成工单报表时发生异常: {str(e)}", exc_info=True)
            
            return None

    def _send_excel_email(self, file_name: str):
        """阶段5：发送带附件的邮件"""
        try:
            file_path = self.report_output or 'output'
            file_full_name = os.path.join(file_path, file_name)
            email_cfg = self.email
            message = MIMEMultipart()

            # 设置发件人名称和邮箱
            sender_name = email_cfg.get('sender_name', 'Tesla Spider')  # 默认发件人名称为 'Tesla Spider'
            message["From"] = email_cfg['sender_email']
            # 修改点1：支持多个收件人配置
            if self.config.get_env() == 'prod':
                receiver_emails = email_cfg['receiver_emails']
            else:
                receiver_emails = email_cfg['receiver_test_emails']
            message["To"] = ", ".join(receiver_emails)  # 多个地址用逗号分隔

            message["Subject"] = f"特斯拉工单基表文件:{file_name}"

            # 添加邮件正文
            body = MIMEText("本次生成的基表见附件excel内容-系统自动发送", "plain")
            message.attach(body)

            # 添加附件
            with open(file_full_name, "rb") as attachment:
                part = MIMEApplication(attachment.read(), Name=file_full_name)
            part['Content-Disposition'] = f'attachment; filename="{os.path.basename(file_name)}"'
            message.attach(part)

            # 发送邮件
            with smtplib.SMTP_SSL(email_cfg['smtp_server'], email_cfg['smtp_port']) as server:
                server.login(email_cfg['sender_email'], email_cfg['sender_password'])
                server.sendmail(email_cfg['sender_email'], receiver_emails, message.as_string())
            self.logger.info(f"📧 Excel文件已发送至 {len(receiver_emails)} 个收件人：{', '.join(receiver_emails)}")
            # 更新订单状态为3--已发送邮件
            self._update_order_status_from_excel(file_full_name)
        except Exception as e:
            error_msg = f"邮件发送失败: {str(e)}"
            self.logger.error(error_msg)
            raise TeslaError("邮件发送失败") from e

    def _update_order_status_from_excel(self, file_path: str):
        """根据Excel更新订单状态"""
        try:
            import pandas as pd

            self.logger.info(f"📋 开始处理Excel文件: {file_path}")
            # 读取Excel文件
            df = pd.read_excel(file_path, usecols=['安装项目编号'])  # 假设列名为'车企订单号'
            order_numbers = df['安装项目编号'].tolist()

            success_count = 0
            for order_no in order_numbers:
                try:
                    # 转换为字符串类型防止类型不匹配
                    order_no_str = str(order_no).strip()
                    if self.dao_manager.update_order_status(order_no_str, status=3):
                        success_count += 1
                    else:
                        self.logger.warning(f"订单号不存在: {order_no_str}")
                except Exception as e:
                    self.logger.error(f"订单状态更新失败: {order_no} | 错误: {str(e)}")

            self.logger.info(f"✅ 状态更新完成，成功 {success_count}/{len(order_numbers)} 条")
        except Exception as e:
            self.logger.error(f"Excel文件处理失败: {str(e)}")
            raise TeslaError("订单状态更新异常") from e

    def update_status(self, order_ids: List[str], status: str) -> bool:
        pass