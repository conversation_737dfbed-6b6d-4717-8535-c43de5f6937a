#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
认证基类模块
提供认证基础功能
"""
import abc
import json
import os
from typing import Dict, Optional

from core.base import BaseObject


class BaseAuth(BaseObject):
    """认证基类，所有认证类的基类"""
    
    MODULE_NAME = None
    
    def __init__(self, config=None):
        """
        初始化认证基类
        
        Args:
            config: 配置对象
        """
        super().__init__(config)
        
        # 认证文件路径
        self.auth_file = None
        # 认证状态
        self.auth_data = None
        # 认证是否已过期
        self.expired = True
        
    def set_auth_file(self, auth_file):
        """
        设置认证文件路径
        
        Args:
            auth_file: 认证文件路径
        """
        self.auth_file = auth_file
        # 确保目录存在
        os.makedirs(os.path.dirname(auth_file), exist_ok=True)
        
    def load_auth_info(self) -> Optional[Dict]:
        """
        加载认证信息
        
        Returns:
            Optional[Dict]: 认证信息，如果加载失败则返回None
        """
        if not self.auth_file or not os.path.exists(self.auth_file):
            return None
            
        try:
            with open(self.auth_file, 'r', encoding='utf-8') as f:
                auth_data = json.load(f)
                self.auth_data = auth_data
                return auth_data
        except Exception as e:
            self.logger.error(f"加载认证信息失败: {str(e)}")
            return None
            
    def save_auth_info(self, auth_info: Dict) -> bool:
        """
        保存认证信息
        
        Args:
            auth_info: 认证信息
            
        Returns:
            bool: 保存是否成功
        """
        if not self.auth_file:
            self.logger.error("未设置认证文件路径，无法保存认证信息")
            return False
            
        try:
            with open(self.auth_file, 'w', encoding='utf-8') as f:
                json.dump(auth_info, f, ensure_ascii=False, indent=2)
                self.auth_data = auth_info
                self.logger.info(f"认证信息已保存到 {self.auth_file}")
                return True
        except Exception as e:
            self.logger.error(f"保存认证信息失败: {str(e)}")
            return False
            
    @abc.abstractmethod
    def login(self) -> Optional[Dict]:
        """
        执行登录操作
        
        Returns:
            Optional[Dict]: 认证信息，如果登录失败则返回None
        """
        pass
        
    @abc.abstractmethod
    def is_expired(self, auth_info: Dict) -> bool:
        """
        检查认证信息是否已过期
        
        Args:
            auth_info: 认证信息
            
        Returns:
            bool: 是否已过期
        """
        pass
        
    @abc.abstractmethod
    def refresh(self, auth_info: Dict) -> Optional[Dict]:
        """
        刷新认证信息
        
        Args:
            auth_info: 旧的认证信息
            
        Returns:
            Optional[Dict]: 新的认证信息，如果刷新失败则返回None
        """
        pass
        
    def get_auth_info(self, force_refresh: bool = False) -> Optional[Dict]:
        """
        获取有效的认证信息
        
        Args:
            force_refresh: 是否强制刷新认证信息
            
        Returns:
            Optional[Dict]: 认证信息，如果无法获取则返回None
        """
        # 加载已有的认证信息
        auth_info = self.load_auth_info()
        
        # 如果没有认证信息或者强制刷新，则执行登录
        if auth_info is None or force_refresh:
            self.logger.info("执行登录...")
            auth_info = self.login()
            if auth_info:
                self.save_auth_info(auth_info)
                return auth_info
            else:
                self.logger.error("登录失败")
                return None
                
        # 检查认证信息是否已过期
        if self.is_expired(auth_info):
            self.logger.info("认证已过期，尝试刷新")
            new_auth_info = self.refresh(auth_info)
            if new_auth_info:
                self.save_auth_info(new_auth_info)
                return new_auth_info
            else:
                self.logger.info("刷新失败，尝试重新登录")
                new_auth_info = self.login()
                if new_auth_info:
                    self.save_auth_info(new_auth_info)
                    return new_auth_info
                else:
                    self.logger.error("重新登录失败")
                    return None
        
        # 认证信息有效
        return auth_info 