#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
存储基类模块
定义数据存储和访问的接口
"""
import abc
import logging
from typing import Dict, List, Optional, Any, Tuple

from core.config import ConfigManager


class BaseStorage(abc.ABC):
    """
    存储基类，定义数据存储和访问的接口
    每个存储子类都必须继承并实现这些方法
    """
    
    def __init__(self, config: ConfigManager):
        """
        初始化存储类
        
        Args:
            config: 配置管理器实例
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
    @abc.abstractmethod
    def connect(self) -> bool:
        """
        连接到存储系统
        
        Returns:
            bool: 连接是否成功
        """
        pass
        
    @abc.abstractmethod
    def disconnect(self) -> None:
        """
        断开与存储系统的连接
        """
        pass
        
    @abc.abstractmethod
    def save(self, data: Dict) -> bool:
        """
        保存单条数据
        
        Args:
            data: 要保存的数据
            
        Returns:
            bool: 保存是否成功
        """
        pass
        
    @abc.abstractmethod
    def bulk_save(self, data_list: List[Dict]) -> Tuple[int, int]:
        """
        批量保存数据
        
        Args:
            data_list: 要保存的数据列表
            
        Returns:
            Tuple[int, int]: 成功保存的数量和失败的数量
        """
        pass
        
    @abc.abstractmethod
    def get(self, id_value: Any) -> Optional[Dict]:
        """
        根据ID获取单条数据
        
        Args:
            id_value: ID值
            
        Returns:
            Optional[Dict]: 获取到的数据，如果不存在则返回None
        """
        pass
        
    @abc.abstractmethod
    def query(self, filter_dict: Dict, limit: Optional[int] = None, 
              offset: Optional[int] = None) -> List[Dict]:
        """
        查询数据
        
        Args:
            filter_dict: 过滤条件字典
            limit: 限制返回的数量
            offset: 跳过的数量
            
        Returns:
            List[Dict]: 查询结果列表
        """
        pass
        
    @abc.abstractmethod
    def update(self, id_value: Any, update_dict: Dict) -> bool:
        """
        更新单条数据
        
        Args:
            id_value: ID值
            update_dict: 要更新的字段字典
            
        Returns:
            bool: 更新是否成功
        """
        pass
        
    @abc.abstractmethod
    def bulk_update(self, id_list: List[Any], update_dict: Dict) -> Tuple[int, int]:
        """
        批量更新数据
        
        Args:
            id_list: ID列表
            update_dict: 要更新的字段字典
            
        Returns:
            Tuple[int, int]: 成功更新的数量和失败的数量
        """
        pass
        
    @abc.abstractmethod
    def delete(self, id_value: Any) -> bool:
        """
        删除单条数据
        
        Args:
            id_value: ID值
            
        Returns:
            bool: 删除是否成功
        """
        pass
        
    @abc.abstractmethod
    def bulk_delete(self, id_list: List[Any]) -> Tuple[int, int]:
        """
        批量删除数据
        
        Args:
            id_list: ID列表
            
        Returns:
            Tuple[int, int]: 成功删除的数量和失败的数量
        """
        pass
        
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect() 