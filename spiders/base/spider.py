#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
爬虫基类模块
定义爬虫基础功能
"""
import abc
from typing import Dict, List, Optional

from core.base import BaseObject
from core.config import ConfigManager


class BaseSpider(BaseObject):
    """爬虫基类，所有爬虫的基类"""
    
    MODULE_NAME = None
    
    def __init__(self, config: ConfigManager):
        """
        初始化爬虫基类
        
        Args:
            config: 配置管理器实例
            notifier: 通知器实例，可选
        """
        super().__init__(config)
        
        # 存储通知器实例
        self.name = self.__class__.__name__.lower().replace('spider', '')
        
    @abc.abstractmethod
    def run(self) -> bool:
        """
        运行爬虫
        
        Returns:
            bool: 执行是否成功
        """
        pass
        
    @abc.abstractmethod
    def sync_to_news(self) -> bool:
        """
        同步数据到News系统
        
        Returns:
            bool: 同步是否成功
        """
        pass
        
    @abc.abstractmethod
    def generate_report(self) -> Optional[str]:
        """
        生成报表
        
        Returns:
            Optional[str]: 报表文件路径，如果生成失败则返回None
        """
        pass
        
    @abc.abstractmethod
    def auth(self) -> Dict:
        """
        登录并获取认证信息
        
        Returns:
            Dict: 认证信息字典
        """
        pass

    @abc.abstractmethod
    def fetch_data(self, auth_info: Dict) -> List[Dict]:
        """
        根据认证信息获取数据
        
        Args:
            auth_info: 认证信息
            
        Returns:
            List[Dict]: 获取到的数据列表
        """
        pass

    @abc.abstractmethod
    def process_data(self, data: List[Dict]) -> List[Dict]:
        """
        处理获取到的数据
        
        Args:
            data: 获取到的原始数据
            
        Returns:
            List[Dict]: 处理后的数据
        """
        pass

    @abc.abstractmethod
    def save_data(self, data: List[Dict]) -> bool:
        """
        保存处理后的数据
        
        Args:
            data: 处理后的数据
            
        Returns:
            bool: 保存是否成功
        """
        pass

    @abc.abstractmethod
    def update_status(self, order_ids: List[str], status: str) -> bool:
        """
        更新数据状态
        
        Args:
            order_ids: 订单ID列表
            status: 新状态
            
        Returns:
            bool: 更新是否成功
        """
        pass