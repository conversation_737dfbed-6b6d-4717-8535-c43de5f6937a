#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据管道基类模块
定义数据处理的流程
"""
import abc
import logging
from typing import Dict, Optional, Any, Callable

from core.config import ConfigManager
from core.notifier.base import BaseNotifier


class BasePipeline(abc.ABC):
    """
    数据管道基类，定义数据处理的流程
    每个管道子类都必须继承并实现这些方法
    """
    
    def __init__(self, config: ConfigManager, notifier: Optional[BaseNotifier] = None):
        """
        初始化数据管道
        
        Args:
            config: 配置管理器实例
            notifier: 通知器实例，用于发送通知
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.notifier = notifier
        self.steps = []  # 管道步骤列表
        
    def add_step(self, name: str, func: Callable, *args, **kwargs) -> None:
        """
        添加管道步骤
        
        Args:
            name: 步骤名称
            func: 步骤函数
            *args: 传递给步骤函数的位置参数
            **kwargs: 传递给步骤函数的关键字参数
        """
        self.steps.append({
            'name': name,
            'func': func,
            'args': args,
            'kwargs': kwargs,
            'status': 'pending'  # pending, running, completed, failed
        })
        
    def run_step(self, step: Dict) -> Any:
        """
        运行单个管道步骤
        
        Args:
            step: 步骤配置字典
            
        Returns:
            Any: 步骤函数的返回值
        """
        step_name = step['name']
        func = step['func']
        args = step['args']
        kwargs = step['kwargs']
        
        self.logger.info(f"开始执行步骤: {step_name}")
        step['status'] = 'running'
        
        try:
            result = func(*args, **kwargs)
            step['status'] = 'completed'
            step['result'] = result
            self.logger.info(f"步骤 {step_name} 执行完成")
            return result
        except Exception as e:
            step['status'] = 'failed'
            step['error'] = str(e)
            self.logger.error(f"步骤 {step_name} 执行失败: {str(e)}", exc_info=True)
            if self.notifier:
                self.notifier.send_alert(
                    title=f"数据管道步骤执行失败: {step_name}",
                    content=f"错误信息: {str(e)}"
                )
            raise
    
    def run(self) -> Dict:
        """
        运行完整的数据管道
        
        Returns:
            Dict: 包含所有步骤状态和结果的字典
        """
        results = {
            'status': 'running',
            'steps': {},
            'success': True
        }
        
        try:
            for step in self.steps:
                step_name = step['name']
                self.logger.info(f"🚀 {step_name} 开始...")
                
                try:
                    step_result = self.run_step(step)
                    results['steps'][step_name] = {
                        'status': 'completed',
                        'result': step_result
                    }
                    self.logger.info(f"✅ {step_name} 完成")
                except Exception as e:
                    results['steps'][step_name] = {
                        'status': 'failed',
                        'error': str(e)
                    }
                    results['success'] = False
                    self.logger.error(f"❌ {step_name} 失败: {str(e)}")
                    
                    # 如果步骤失败且配置为终止，则中断管道
                    if self.config.get('pipeline.fail_fast', True):
                        self.logger.error(f"管道执行中止，因为步骤 {step_name} 失败")
                        break
            
            # 设置最终状态
            results['status'] = 'completed' if results['success'] else 'failed'
            
            # 发送完成通知
            if self.notifier:
                if results['success']:
                    self.notifier.send_message(
                        title="数据管道执行成功",
                        content=f"所有步骤已完成"
                    )
                else:
                    failed_steps = [name for name, info in results['steps'].items() if info['status'] == 'failed']
                    self.notifier.send_alert(
                        title="数据管道执行失败",
                        content=f"失败步骤: {', '.join(failed_steps)}"
                    )
            
            return results
        except Exception as e:
            self.logger.error(f"管道执行过程中发生未处理异常: {str(e)}", exc_info=True)
            results['status'] = 'failed'
            results['error'] = str(e)
            results['success'] = False
            
            if self.notifier:
                self.notifier.send_alert(
                    title="数据管道执行发生未处理异常",
                    content=f"错误信息: {str(e)}"
                )
            
            return results 