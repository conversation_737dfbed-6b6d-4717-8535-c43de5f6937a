"""---------------------- 异常体系 ----------------------"""
class TeslaError(Exception):
    """基础业务异常"""
    pass


class AuthError(TeslaError):
    """认证相关异常"""
    pass


class NetworkError(TeslaError):
    """网络请求异常"""
    pass


class BrowserError(TeslaError):
    """浏览器操作异常"""
    pass

class TeslaError(Exception):
    """基础业务异常"""
    def __init__(self, message="特斯拉业务处理异常"):
        super().__init__(message)

class APIConnectionError(TeslaError):
    """API连接异常"""
    def __init__(self, url: str):
        super().__init__(f"API连接失败: {url}")

class DataIntegrityError(TeslaError):
    """数据完整性异常"""
    def __init__(self, field: str):
        super().__init__(f"数据校验失败: {field} 字段缺失")
