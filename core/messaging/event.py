#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
事件总线模块
用于系统内部事件的发布和订阅
"""
import logging
from datetime import datetime
from typing import Dict, Callable

from core.config import ConfigManager
from core.messaging.queue import Producer, Consumer


class Event:
    """事件类，表示系统中的一个事件"""
    
    def __init__(self, event_type: str, data: Dict = None, source: str = None):
        """
        初始化事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
            source: 事件源
        """
        self.event_type = event_type
        self.data = data or {}
        self.source = source
        self.timestamp = datetime.now().isoformat()
        
    def to_dict(self) -> Dict:
        """
        将事件转换为字典
        
        Returns:
            Dict: 事件字典
        """
        return {
            'event_type': self.event_type,
            'data': self.data,
            'source': self.source,
            'timestamp': self.timestamp
        }
        
    @classmethod
    def from_dict(cls, data: Dict) -> 'Event':
        """
        从字典创建事件
        
        Args:
            data: 事件字典
            
        Returns:
            Event: 事件对象
        """
        event = cls(
            event_type=data['event_type'],
            data=data.get('data', {}),
            source=data.get('source')
        )
        event.timestamp = data.get('timestamp', datetime.now().isoformat())
        return event


class EventPublisher:
    """事件发布器，用于发布事件"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化事件发布器
        
        Args:
            config: 配置管理器实例
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config
        self.producer = Producer(config)
        
        # 事件路由键前缀
        self.event_prefix = config.get('RabbitMQ.event_prefix', 'spider.event')
        
    def publish(self, event: Event) -> bool:
        """
        发布事件
        
        Args:
            event: 事件对象
            
        Returns:
            bool: 发布是否成功
        """
        try:
            # 构建路由键
            routing_key = f"{self.event_prefix}.{event.event_type}"
            
            # 发送事件
            result = self.producer.send(event.to_dict(), routing_key)
            
            if result:
                self.logger.debug(f"事件已发布: {event.event_type}")
            else:
                self.logger.error(f"事件发布失败: {event.event_type}")
                
            return result
        except Exception as e:
            self.logger.error(f"发布事件时发生异常: {str(e)}")
            return False
            
    def close(self) -> None:
        """关闭事件发布器"""
        self.producer.close()
        
    def __enter__(self) -> 'EventPublisher':
        """上下文管理器入口"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """上下文管理器出口"""
        self.close()


class EventSubscriber:
    """事件订阅器，用于订阅和处理事件"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化事件订阅器
        
        Args:
            config: 配置管理器实例
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config
        self.consumer = Consumer(config)
        
        # 事件队列名称
        self.event_queue = config.get('RabbitMQ.event_queue', 'spider_event_queue')
        
        # 事件处理器
        self.event_handlers = {}
        
    def subscribe(self, event_type: str, handler: Callable) -> None:
        """
        订阅事件
        
        Args:
            event_type: 事件类型
            handler: 处理函数，接收Event对象作为参数
        """
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = set()
            
        self.event_handlers[event_type].add(handler)
        self.logger.info(f"已订阅事件: {event_type}")
        
    def unsubscribe(self, event_type: str, handler: Callable) -> None:
        """
        取消订阅事件
        
        Args:
            event_type: 事件类型
            handler: 处理函数
        """
        if event_type in self.event_handlers:
            self.event_handlers[event_type].discard(handler)
            self.logger.info(f"已取消订阅事件: {event_type}")
            
    def handle_event(self, event_data: Dict) -> None:
        """
        处理事件
        
        Args:
            event_data: 事件数据字典
        """
        try:
            # 解析事件
            event = Event.from_dict(event_data)
            
            self.logger.debug(f"接收到事件: {event.event_type}")
            
            # 查找处理器
            handlers = self.event_handlers.get(event.event_type, set())
            
            # 添加通配符处理器
            wildcard_handlers = self.event_handlers.get('*', set())
            handlers = handlers.union(wildcard_handlers)
            
            if handlers:
                for handler in handlers:
                    try:
                        handler(event)
                    except Exception as e:
                        self.logger.error(f"调用事件处理器时发生异常: {str(e)}")
            else:
                self.logger.debug(f"没有找到事件 {event.event_type} 的处理器")
        except Exception as e:
            self.logger.error(f"处理事件时发生异常: {str(e)}")
            
    def start(self) -> None:
        """开始监听事件"""
        try:
            # 定义消息回调函数
            def callback(message):
                if isinstance(message, dict) and 'event_type' in message:
                    self.handle_event(message)
                    
            # 开始消费
            self.consumer.start(callback, self.event_queue)
        except Exception as e:
            self.logger.error(f"启动事件订阅器时发生异常: {str(e)}")
            
    def stop(self) -> None:
        """停止监听事件"""
        self.consumer.stop()
        
    def __enter__(self) -> 'EventSubscriber':
        """上下文管理器入口"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """上下文管理器出口"""
        self.stop()


class EventBus:
    """事件总线，集成事件发布和订阅功能"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化事件总线
        
        Args:
            config: 配置管理器实例
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config
        self.publisher = EventPublisher(config)
        self.subscriber = EventSubscriber(config)
        
        # 本地事件处理器，不通过消息队列传递
        self.local_handlers = {}
        
    def publish(self, event: Event) -> bool:
        """
        发布事件
        
        Args:
            event: 事件对象
            
        Returns:
            bool: 发布是否成功
        """
        # 先处理本地事件
        self._handle_local_event(event)
        
        # 再发布到消息队列
        return self.publisher.publish(event)
        
    def subscribe(self, event_type: str, handler: Callable, local_only: bool = False) -> None:
        """
        订阅事件
        
        Args:
            event_type: 事件类型
            handler: 处理函数，接收Event对象作为参数
            local_only: 是否只处理本地事件，不通过消息队列
        """
        if local_only:
            if event_type not in self.local_handlers:
                self.local_handlers[event_type] = set()
                
            self.local_handlers[event_type].add(handler)
            self.logger.info(f"已订阅本地事件: {event_type}")
        else:
            self.subscriber.subscribe(event_type, handler)
            
    def unsubscribe(self, event_type: str, handler: Callable, local_only: bool = False) -> None:
        """
        取消订阅事件
        
        Args:
            event_type: 事件类型
            handler: 处理函数
            local_only: 是否只取消本地事件订阅
        """
        if local_only:
            if event_type in self.local_handlers:
                self.local_handlers[event_type].discard(handler)
                self.logger.info(f"已取消订阅本地事件: {event_type}")
        else:
            self.subscriber.unsubscribe(event_type, handler)
            
    def _handle_local_event(self, event: Event) -> None:
        """
        处理本地事件
        
        Args:
            event: 事件对象
        """
        try:
            # 查找处理器
            handlers = self.local_handlers.get(event.event_type, set())
            
            # 添加通配符处理器
            wildcard_handlers = self.local_handlers.get('*', set())
            handlers = handlers.union(wildcard_handlers)
            
            if handlers:
                for handler in handlers:
                    try:
                        handler(event)
                    except Exception as e:
                        self.logger.error(f"调用本地事件处理器时发生异常: {str(e)}")
        except Exception as e:
            self.logger.error(f"处理本地事件时发生异常: {str(e)}")
            
    def start(self) -> None:
        """启动事件总线"""
        self.subscriber.start()
        
    def stop(self) -> None:
        """停止事件总线"""
        self.subscriber.stop()
        self.publisher.close()
        
    def __enter__(self) -> 'EventBus':
        """上下文管理器入口"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """上下文管理器出口"""
        self.stop() 