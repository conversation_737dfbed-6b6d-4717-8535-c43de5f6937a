#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
任务队列模块
用于管理分布式任务
"""
import logging
import uuid
from datetime import datetime
from typing import Dict, Optional, Callable

from core.config import ConfigManager
from core.messaging.queue import Producer, Consumer


class Task:
    """任务类，表示一个待执行的任务"""
    
    def __init__(self, task_type: str, params: Dict, task_id: Optional[str] = None):
        """
        初始化任务
        
        Args:
            task_type: 任务类型
            params: 任务参数
            task_id: 任务ID，如果未提供则自动生成
        """
        self.task_id = task_id or str(uuid.uuid4())
        self.task_type = task_type
        self.params = params
        self.created_at = datetime.now().isoformat()
        self.status = 'pending'  # pending, running, completed, failed
        self.result = None
        self.error = None
        
    def to_dict(self) -> Dict:
        """
        将任务转换为字典
        
        Returns:
            Dict: 任务字典
        """
        return {
            'task_id': self.task_id,
            'task_type': self.task_type,
            'params': self.params,
            'created_at': self.created_at,
            'status': self.status,
            'result': self.result,
            'error': self.error
        }
        
    @classmethod
    def from_dict(cls, data: Dict) -> 'Task':
        """
        从字典创建任务
        
        Args:
            data: 任务字典
            
        Returns:
            Task: 任务对象
        """
        task = cls(
            task_type=data['task_type'],
            params=data['params'],
            task_id=data['task_id']
        )
        task.created_at = data.get('created_at', datetime.now().isoformat())
        task.status = data.get('status', 'pending')
        task.result = data.get('result')
        task.error = data.get('error')
        return task


class TaskProducer:
    """任务生产者，用于发布任务到队列"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化任务生产者
        
        Args:
            config: 配置管理器实例
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config
        self.producer = Producer(config)
        
        # 任务路由键前缀
        self.task_prefix = config.get('RabbitMQ.task_prefix', 'spider.task')
        
    def submit_task(self, task: Task) -> bool:
        """
        提交任务到队列
        
        Args:
            task: 任务对象
            
        Returns:
            bool: 提交是否成功
        """
        try:
            # 构建路由键
            routing_key = f"{self.task_prefix}.{task.task_type}"
            
            # 发送任务
            result = self.producer.send(task.to_dict(), routing_key)
            
            if result:
                self.logger.info(f"任务已提交: {task.task_id} ({task.task_type})")
            else:
                self.logger.error(f"任务提交失败: {task.task_id}")
                
            return result
        except Exception as e:
            self.logger.error(f"提交任务时发生异常: {str(e)}")
            return False
            
    def close(self) -> None:
        """关闭任务生产者"""
        self.producer.close()
        
    def __enter__(self) -> 'TaskProducer':
        """上下文管理器入口"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """上下文管理器出口"""
        self.close()


class TaskConsumer:
    """任务消费者，用于处理队列中的任务"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化任务消费者
        
        Args:
            config: 配置管理器实例
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config
        self.consumer = Consumer(config)
        
        # 任务队列名称
        self.task_queue = config.get('RabbitMQ.task_queue', 'spider_task_queue')
        
        # 任务处理器
        self.task_handlers = {}
        
    def register_handler(self, task_type: str, handler: Callable) -> None:
        """
        注册任务处理器
        
        Args:
            task_type: 任务类型
            handler: 处理函数，接收Task对象作为参数，返回处理结果
        """
        self.task_handlers[task_type] = handler
        self.logger.info(f"已注册任务处理器: {task_type}")
        
    def handle_task(self, task_data: Dict) -> None:
        """
        处理任务
        
        Args:
            task_data: 任务数据字典
        """
        try:
            # 解析任务
            task = Task.from_dict(task_data)
            
            # 更新任务状态
            task.status = 'running'
            
            self.logger.info(f"开始处理任务: {task.task_id} ({task.task_type})")
            
            # 查找处理器
            handler = self.task_handlers.get(task.task_type)
            if handler:
                try:
                    # 执行任务
                    result = handler(task)
                    
                    # 更新任务状态
                    task.status = 'completed'
                    task.result = result
                    
                    self.logger.info(f"任务处理完成: {task.task_id}")
                except Exception as e:
                    # 任务执行失败
                    task.status = 'failed'
                    task.error = str(e)
                    
                    self.logger.error(f"任务处理失败: {task.task_id}, 错误: {str(e)}")
            else:
                # 没有找到处理器
                task.status = 'failed'
                task.error = f"未找到任务类型 {task.task_type} 的处理器"
                
                self.logger.error(f"未找到任务类型 {task.task_type} 的处理器")
                
            # 这里可以加入任务结果的处理逻辑，如保存到数据库、发送结果通知等
        except Exception as e:
            self.logger.error(f"处理任务时发生异常: {str(e)}")
            
    def start(self) -> None:
        """开始消费任务"""
        try:
            # 定义消息回调函数
            def callback(message):
                if isinstance(message, dict) and 'task_id' in message and 'task_type' in message:
                    self.handle_task(message)
                    
            # 开始消费
            self.consumer.start(callback, self.task_queue)
        except Exception as e:
            self.logger.error(f"启动任务消费者时发生异常: {str(e)}")
            
    def stop(self) -> None:
        """停止消费任务"""
        self.consumer.stop()
        
    def __enter__(self) -> 'TaskConsumer':
        """上下文管理器入口"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """上下文管理器出口"""
        self.stop() 