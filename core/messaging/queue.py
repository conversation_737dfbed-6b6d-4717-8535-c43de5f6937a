#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
消息队列管理模块
用于处理消息队列的连接和消息发送接收
"""
import json
import logging
import time
from typing import Dict, Optional, Callable, Union

import pika
from pika.exceptions import AMQPConnectionError, AMQPChannelError

from core.config import ConfigManager


class QueueManager:
    """消息队列管理器，用于处理消息队列操作"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化消息队列管理器
        
        Args:
            config: 配置管理器实例
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config
        
        # 从配置中获取RabbitMQ连接信息
        self.host = config.get('RabbitMQ.host', 'localhost')
        self.port = config.get('RabbitMQ.port', 5672)
        self.vhost = config.get('RabbitMQ.vhost', '/')
        self.username = config.get('RabbitMQ.username', 'guest')
        self.password = config.get('RabbitMQ.password', 'guest')
        self.exchange = config.get('RabbitMQ.exchange', 'spider_exchange')
        self.exchange_type = config.get('RabbitMQ.exchange_type', 'topic')
        self.queue_name = config.get('RabbitMQ.queue', 'spider_queue')
        self.routing_key = config.get('RabbitMQ.routing_key', 'spider.#')
        self.connection_attempts = config.get('RabbitMQ.connection_attempts', 3)
        self.retry_delay = config.get('RabbitMQ.retry_delay', 5)
        
        # 连接和通道
        self.connection = None
        self.channel = None
        
        self.logger.info(f"消息队列管理器初始化完成，RabbitMQ服务器: {self.host}:{self.port}")
        
    def connect(self) -> bool:
        """
        连接到RabbitMQ服务器
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 创建连接参数
            credentials = pika.PlainCredentials(self.username, self.password)
            parameters = pika.ConnectionParameters(
                host=self.host,
                port=self.port,
                virtual_host=self.vhost,
                credentials=credentials,
                connection_attempts=self.connection_attempts,
                retry_delay=self.retry_delay
            )
            
            # 建立连接
            self.logger.info(f"正在连接到RabbitMQ服务器: {self.host}:{self.port}")
            self.connection = pika.BlockingConnection(parameters)
            
            # 创建通道
            self.channel = self.connection.channel()
            
            # 声明交换机
            self.channel.exchange_declare(
                exchange=self.exchange,
                exchange_type=self.exchange_type,
                durable=True
            )
            
            # 声明队列
            self.channel.queue_declare(
                queue=self.queue_name,
                durable=True
            )
            
            # 绑定队列到交换机
            self.channel.queue_bind(
                queue=self.queue_name,
                exchange=self.exchange,
                routing_key=self.routing_key
            )
            
            self.logger.info("RabbitMQ连接成功")
            return True
        except AMQPConnectionError as e:
            self.logger.error(f"RabbitMQ连接失败: {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"连接RabbitMQ时发生异常: {str(e)}")
            return False
            
    def disconnect(self) -> None:
        """断开与RabbitMQ服务器的连接"""
        try:
            if self.channel and self.channel.is_open:
                self.channel.close()
                
            if self.connection and self.connection.is_open:
                self.connection.close()
                
            self.logger.info("RabbitMQ连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭RabbitMQ连接时发生异常: {str(e)}")
            
    def publish(self, message: Union[str, Dict], routing_key: Optional[str] = None) -> bool:
        """
        发布消息到队列
        
        Args:
            message: 要发布的消息，可以是字符串或字典
            routing_key: 路由键，默认使用配置中的routing_key
            
        Returns:
            bool: 发布是否成功
        """
        if not self.connection or not self.channel:
            if not self.connect():
                return False
                
        try:
            # 处理消息
            if isinstance(message, dict):
                message = json.dumps(message)
                
            # 使用默认路由键
            if routing_key is None:
                routing_key = self.routing_key
                
            # 发布消息
            self.channel.basic_publish(
                exchange=self.exchange,
                routing_key=routing_key,
                body=message,
                properties=pika.BasicProperties(
                    delivery_mode=2,  # 持久化消息
                    content_type='application/json'
                )
            )
            
            self.logger.info(f"消息已发布: {routing_key}")
            return True
        except Exception as e:
            self.logger.error(f"发布消息失败: {str(e)}")
            return False
            
    def consume(self, callback: Callable, queue_name: Optional[str] = None) -> None:
        """
        消费队列中的消息
        
        Args:
            callback: 回调函数，接收消息体作为参数
            queue_name: 队列名称，默认使用配置中的queue_name
        """
        if not self.connection or not self.channel:
            if not self.connect():
                return
                
        try:
            # 使用默认队列名
            if queue_name is None:
                queue_name = self.queue_name
                
            # 设置回调函数
            def wrapped_callback(ch, method, properties, body):
                try:
                    # 尝试解析JSON消息
                    message = body.decode('utf-8')
                    try:
                        message = json.loads(message)
                    except:
                        pass
                        
                    # 调用回调函数
                    callback(message)
                    
                    # 确认消息处理完成
                    ch.basic_ack(delivery_tag=method.delivery_tag)
                except Exception as e:
                    self.logger.error(f"处理消息时发生异常: {str(e)}")
                    # 拒绝消息并重新入队
                    ch.basic_nack(delivery_tag=method.delivery_tag, requeue=True)
            
            # 设置预取计数，每次只接收一条消息
            self.channel.basic_qos(prefetch_count=1)
            
            # 开始消费
            self.channel.basic_consume(
                queue=queue_name,
                on_message_callback=wrapped_callback
            )
            
            self.logger.info(f"开始消费队列: {queue_name}")
            
            # 开始事件循环
            self.channel.start_consuming()
        except (AMQPConnectionError, AMQPChannelError) as e:
            self.logger.error(f"RabbitMQ连接异常: {str(e)}")
            # 尝试重新连接
            time.sleep(self.retry_delay)
            self.connect()
            self.consume(callback, queue_name)
        except Exception as e:
            self.logger.error(f"消费消息时发生异常: {str(e)}")
            
    def __enter__(self) -> 'QueueManager':
        """上下文管理器入口"""
        self.connect()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """上下文管理器出口"""
        self.disconnect()


class Producer:
    """消息生产者，用于发送消息到队列"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化消息生产者
        
        Args:
            config: 配置管理器实例
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.queue_manager = QueueManager(config)
        
    def send(self, message: Union[str, Dict], routing_key: Optional[str] = None) -> bool:
        """
        发送消息
        
        Args:
            message: 要发送的消息，可以是字符串或字典
            routing_key: 路由键
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 连接到RabbitMQ
            if not self.queue_manager.connection or not self.queue_manager.connection.is_open:
                if not self.queue_manager.connect():
                    return False
                    
            # 发布消息
            return self.queue_manager.publish(message, routing_key)
        except Exception as e:
            self.logger.error(f"发送消息失败: {str(e)}")
            return False
            
    def close(self) -> None:
        """关闭生产者"""
        self.queue_manager.disconnect()
        
    def __enter__(self) -> 'Producer':
        """上下文管理器入口"""
        self.queue_manager.connect()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """上下文管理器出口"""
        self.queue_manager.disconnect()


class Consumer:
    """消息消费者，用于从队列接收消息"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化消息消费者
        
        Args:
            config: 配置管理器实例
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.queue_manager = QueueManager(config)
        
    def start(self, callback: Callable, queue_name: Optional[str] = None) -> None:
        """
        开始消费消息
        
        Args:
            callback: 回调函数，接收消息体作为参数
            queue_name: 队列名称
        """
        try:
            # 连接到RabbitMQ
            if not self.queue_manager.connection or not self.queue_manager.connection.is_open:
                if not self.queue_manager.connect():
                    return
                    
            # 开始消费
            self.queue_manager.consume(callback, queue_name)
        except Exception as e:
            self.logger.error(f"开始消费消息失败: {str(e)}")
            
    def stop(self) -> None:
        """停止消费消息"""
        try:
            if self.queue_manager.channel and self.queue_manager.channel.is_open:
                self.queue_manager.channel.stop_consuming()
                
            self.queue_manager.disconnect()
        except Exception as e:
            self.logger.error(f"停止消费消息失败: {str(e)}")
            
    def __enter__(self) -> 'Consumer':
        """上下文管理器入口"""
        self.queue_manager.connect()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """上下文管理器出口"""
        self.stop() 