#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于消息队列的通知模块
实现通知消息的发送和接收
"""
import logging
from typing import Dict, List, Optional, Callable

from core.config import ConfigManager
from core.messaging.queue import Producer, Consumer


class QueueNotifier:
    """基于消息队列的通知器"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化消息队列通知器
        
        Args:
            config: 配置管理器实例
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config
        self.producer = Producer(config)
        
        # 通知路由键前缀
        self.notification_prefix = config.get('RabbitMQ.notification_prefix', 'spider.notification')
        
    def send_notification(self, message: Dict, level: str = 'info') -> bool:
        """
        发送通知消息
        
        Args:
            message: 通知消息字典
            level: 通知级别，如info, warning, error等
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 构建消息体
            notification = {
                'type': 'notification',
                'level': level,
                'message': message
            }
            
            # 构建路由键
            routing_key = f"{self.notification_prefix}.{level}"
            
            # 发送消息
            result = self.producer.send(notification, routing_key)
            
            if result:
                self.logger.info(f"通知消息已发送: {level}")
            else:
                self.logger.error("通知消息发送失败")
                
            return result
        except Exception as e:
            self.logger.error(f"发送通知消息时发生异常: {str(e)}")
            return False
            
    def send_info(self, title: str, content: str, **kwargs) -> bool:
        """
        发送信息通知
        
        Args:
            title: 通知标题
            content: 通知内容
            **kwargs: 其他通知参数
            
        Returns:
            bool: 发送是否成功
        """
        message = {
            'title': title,
            'content': content,
            **kwargs
        }
        return self.send_notification(message, 'info')
        
    def send_warning(self, title: str, content: str, **kwargs) -> bool:
        """
        发送警告通知
        
        Args:
            title: 通知标题
            content: 通知内容
            **kwargs: 其他通知参数
            
        Returns:
            bool: 发送是否成功
        """
        message = {
            'title': title,
            'content': content,
            **kwargs
        }
        return self.send_notification(message, 'warning')
        
    def send_error(self, title: str, content: str, error_msg: Optional[str] = None, **kwargs) -> bool:
        """
        发送错误通知
        
        Args:
            title: 通知标题
            content: 通知内容
            error_msg: 错误信息
            **kwargs: 其他通知参数
            
        Returns:
            bool: 发送是否成功
        """
        message = {
            'title': title,
            'content': content,
            **kwargs
        }
        
        if error_msg:
            message['error'] = error_msg
            
        return self.send_notification(message, 'error')
        
    def close(self) -> None:
        """关闭通知器"""
        self.producer.close()
        
    def __enter__(self) -> 'QueueNotifier':
        """上下文管理器入口"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """上下文管理器出口"""
        self.close()


class NotificationListener:
    """通知消息监听器"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化通知消息监听器
        
        Args:
            config: 配置管理器实例
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config
        self.consumer = Consumer(config)
        
        # 通知队列名称
        self.notification_queue = config.get('RabbitMQ.notification_queue', 'spider_notification_queue')
        
        # 回调处理器
        self.handlers = {
            'info': [],
            'warning': [],
            'error': [],
            'all': []
        }
        
    def register_handler(self, handler: Callable, levels: Optional[List[str]] = None) -> None:
        """
        注册通知处理器
        
        Args:
            handler: 处理函数，接收通知消息作为参数
            levels: 要处理的通知级别列表，如果为None则处理所有级别
        """
        if levels is None:
            self.handlers['all'].append(handler)
        else:
            for level in levels:
                if level in self.handlers:
                    self.handlers[level].append(handler)
                else:
                    self.logger.warning(f"未知的通知级别: {level}")
                    
    def handle_notification(self, notification: Dict) -> None:
        """
        处理通知消息
        
        Args:
            notification: 通知消息
        """
        try:
            # 获取通知级别
            level = notification.get('level', 'info')
            
            # 调用对应级别的处理器
            if level in self.handlers:
                for handler in self.handlers[level]:
                    try:
                        handler(notification)
                    except Exception as e:
                        self.logger.error(f"调用通知处理器时发生异常: {str(e)}")
                        
            # 调用处理所有级别的处理器
            for handler in self.handlers['all']:
                try:
                    handler(notification)
                except Exception as e:
                    self.logger.error(f"调用通知处理器时发生异常: {str(e)}")
        except Exception as e:
            self.logger.error(f"处理通知消息时发生异常: {str(e)}")
            
    def start(self) -> None:
        """开始监听通知消息"""
        try:
            # 定义消息回调函数
            def callback(message):
                if isinstance(message, dict) and message.get('type') == 'notification':
                    self.handle_notification(message)
                    
            # 开始消费
            self.consumer.start(callback, self.notification_queue)
        except Exception as e:
            self.logger.error(f"启动通知监听器时发生异常: {str(e)}")
            
    def stop(self) -> None:
        """停止监听通知消息"""
        self.consumer.stop()
        
    def __enter__(self) -> 'NotificationListener':
        """上下文管理器入口"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """上下文管理器出口"""
        self.stop() 