#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基础模块
提供基础的配置和日志功能
"""
import logging
import sys
from typing import Any, Dict


class BasicConfig:
    """基础配置类，提供简单的配置功能"""
    
    def __init__(self, config_data: Dict[str, Any] = None):
        """
        初始化基础配置
        
        Args:
            config_data: 配置数据字典
        """
        self.config_data = config_data or {}
        
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的多级键
            default: 默认值，当键不存在时返回
            
        Returns:
            Any: 配置值
        """
        parts = key.split('.')
        data = self.config_data
        
        for part in parts:
            if isinstance(data, dict) and part in data:
                data = data[part]
            else:
                return default
                
        return data


def setup_basic_logger(name: str) -> logging.Logger:
    """
    设置基本的日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        logging.Logger: 配置好的日志器
    """
    logger = logging.getLogger(name)
    logger.propagate = False  # 防止日志向上传播，避免重复
    
    # 如果已经有处理器，直接返回现有的logger
    if logger.handlers:
        return logger
        
    # 添加基本的控制台处理器
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
    
    return logger


class BaseObject:
    """
    所有对象的基类
    提供基础功能，如日志记录、配置获取等通用功能
    
    使用示例:
    ```python
    class MyModule(BaseObject):
        MODULE_NAME = 'mymodule'  # 对应conf/mymodule_config.yaml
        
        def __init__(self, config):
            super().__init__(config)
            # 使用继承的_get_config方法获取配置
            self.value = self._get_config('some.key', 'default_value')
    ```
    """
    
    def __init__(self, config=None):
        """
        初始化基础对象
        
        Args:
            config: 配置对象，可选
        """
        # 记录配置对象
        self.config = config
        
        # 如果传入了配置对象，可以从中获取模块特定配置
        self.module_config = None
        if hasattr(self, 'MODULE_NAME') and self.config:
            try:
                self.module_config = self.config.load_spider_config(self.MODULE_NAME)
            except:
                pass
        
        # 获取日志器
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 确保日志级别设置正确（从配置获取或使用默认值）
        if self.config:
            log_level_name = self.config.get('logging.level', 'INFO').upper()
            log_level = getattr(logging, log_level_name, logging.INFO)
            self.logger.setLevel(log_level)
            # 确保日志可以正常传播到根日志器
            self.logger.propagate = True
        
    def _get_config(self, key: str, default: Any = None) -> Any:
        """
        从模块配置或全局配置中获取配置值
        
        首先尝试从模块配置(self.module_config)中获取，
        如果找不到再从全局配置(self.config)中获取，
        如果仍然找不到则返回默认值。
        
        Args:
            key: 配置键，支持点号分隔的多级键
            default: 默认值，当键不存在时返回
            
        Returns:
            Any: 配置值
        """
        # 如果模块配置存在，先从模块配置中查找
        if self.module_config:
            # 从模块配置中查找
            parts = key.split('.')
            data = self.module_config
            
            for part in parts:
                if isinstance(data, dict) and part in data:
                    data = data[part]
                else:
                    # 模块配置中不存在，返回None以便后续尝试从全局配置中获取
                    data = None
                    break
            
            # 如果在模块配置中找到值，返回它
            if data is not None:
                return data
        
        # 如果有全局配置对象，尝试从全局配置中获取
        if self.config:
            return self.config.get(key, default)
        
        # 如果没有找到，返回默认值
        return default 