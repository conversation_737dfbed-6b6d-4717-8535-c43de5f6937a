#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志配置模块
管理日志的配置和初始化
"""
import logging
import logging.config
import os
import time
from typing import Optional


def configure_logging(config_obj=None, config_path: Optional[str] = None) -> None:
    """
    配置日志系统
    
    Args:
        config_obj: 配置对象，如果为None则使用config_path加载ConfigManager
        config_path: 配置文件路径，默认为None
    """
    # 输出调试信息
    print("=== 开始配置日志系统 ===")
    
    # 清理所有现有的日志器处理器，避免重复
    for logger_name in list(logging.root.manager.loggerDict.keys()):
        logger = logging.getLogger(logger_name)
        # 设置propagate=False以防止日志向上传播
        logger.propagate = False
        # 清除现有处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
    
    # 清理根日志器处理器
    root_logger = logging.getLogger()
    root_logger.propagate = False
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 加载配置
    if config_obj is None:
        # 延迟导入ConfigManager避免循环依赖
        from core.config import ConfigManager
        config_obj = ConfigManager(config_path)
    
    # 获取日志配置
    log_level_name = config_obj.get('logging.level', 'INFO').upper()
    print(f"从配置中获取的日志级别: {log_level_name}")
    
    # 确保日志目录存在
    log_dir = config_obj.get('logging.storage_dir', 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    
    # 日志文件名
    log_filename_format = config_obj.get('logging.filename_format', '%Y-%m-%d_%H-%M-%S.log')
    log_filename = time.strftime(log_filename_format, time.localtime())
    log_filepath = os.path.join(log_dir, log_filename)
    
    # 轮转设置
    max_bytes = config_obj.get('logging.max_bytes', 10 * 1024 * 1024)  # 默认10MB
    backup_count = config_obj.get('logging.backup_count', 5)
    
    # 日志格式
    log_format = config_obj.get('logging.format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 使用dictConfig方式配置日志系统
    logging_config = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'standard': {
                'format': log_format
            },
        },
        'handlers': {
            'console': {
                'level': log_level_name,
                'class': 'logging.StreamHandler',
                'formatter': 'standard',
                'stream': 'ext://sys.stdout',
            },
            'file': {
                'level': log_level_name,
                'class': 'logging.handlers.RotatingFileHandler',
                'formatter': 'standard',
                'filename': log_filepath,
                'maxBytes': max_bytes,
                'backupCount': backup_count,
                'encoding': 'utf-8',
            },
        },
        'loggers': {
            '': {  # root logger
                'handlers': ['console', 'file'] if config_obj.get('logging.console_output', True) else ['file'],
                'level': log_level_name,
                'propagate': False,
            },
            # 第三方库日志控制
            'urllib3': {'level': 'WARNING'},
            'sqlalchemy': {'level': 'WARNING'},
            'playwright': {'level': 'WARNING'},
        }
    }
    
    # 应用配置
    logging.config.dictConfig(logging_config)
    
    # 验证配置是否生效
    root_logger = logging.getLogger()
    print(f"根日志器当前级别: {logging.getLevelName(root_logger.level)}")
    print(f"DEBUG级别值: {logging.DEBUG}, INFO级别值: {logging.INFO}")
    print(f"测试DEBUG日志是否可见: {root_logger.isEnabledFor(logging.DEBUG)}")
    
    # 尝试记录不同级别的日志
    root_logger.debug("DEBUG测试消息 - 应该在DEBUG级别或更低时可见")
    root_logger.info("INFO测试消息 - 应该在INFO级别或更低时可见")
    
    # 使用root logger记录，避免创建新的logger
    logging.root.info("日志系统已配置")
    print("=== 日志系统配置完成 ===")


class Logger:
    """日志工具类，提供静态方法获取日志器"""
    
    @staticmethod
    def get_logger(name: str) -> logging.Logger:
        """
        获取指定名称的日志器
        
        Args:
            name: 日志器名称
            
        Returns:
            logging.Logger: 日志器实例
        """
        return logging.getLogger(name) 