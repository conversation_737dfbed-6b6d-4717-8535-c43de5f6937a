#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
HTTP请求工具模块
用于处理HTTP请求
"""
import logging
import time
from typing import Dict, Optional, Any
from urllib.parse import urlparse

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from core.config import ConfigManager


class RequestsManager:
    """HTTP请求管理器，用于处理HTTP请求"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化HTTP请求管理器
        
        Args:
            config: 配置管理器实例
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config
        
        # 从配置中获取请求设置
        self.timeout = config.get('http.timeout', 30)
        self.max_retries = config.get('http.max_retries', 3)
        self.retry_backoff_factor = config.get('http.retry_backoff_factor', 0.3)
        self.retry_status_forcelist = config.get('http.retry_status_forcelist', [500, 502, 503, 504])
        self.verify_ssl = config.get('http.verify_ssl', True)
        self.default_user_agent = config.get('http.user_agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
        
        # 创建会话，配置重试策略
        self.session = self._create_session()
        
    def _create_session(self) -> requests.Session:
        """
        创建请求会话并配置重试策略
        
        Returns:
            requests.Session: 配置好的会话
        """
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=self.max_retries,
            backoff_factor=self.retry_backoff_factor,
            status_forcelist=self.retry_status_forcelist,
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST", "PUT", "DELETE", "PATCH"]
        )
        
        # 配置适配器
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 配置默认请求头
        session.headers.update({
            'User-Agent': self.default_user_agent,
        })
        
        return session
        
    def request(self, method: str, url: str, **kwargs) -> requests.Response:
        """
        发送HTTP请求
        
        Args:
            method: 请求方法，如'GET'、'POST'等
            url: 请求URL
            **kwargs: 其他请求参数，如headers、params、data、json等
            
        Returns:
            requests.Response: 响应对象
        """
        # 未设置超时时使用默认超时
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.timeout
            
        # 处理SSL验证
        if 'verify' not in kwargs:
            kwargs['verify'] = self.verify_ssl
            
        # 记录请求日志
        parsed_url = urlparse(url)
        masked_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"
        self.logger.debug(f"发送 {method.upper()} 请求: {masked_url}")
        
        # 请求计时
        start_time = time.time()
        
        try:
            response = self.session.request(method, url, **kwargs)
            elapsed_time = time.time() - start_time
            
            # 记录响应日志
            self.logger.debug(
                f"收到响应: {response.status_code}, 用时: {elapsed_time:.2f}秒, "
                f"大小: {len(response.content)} 字节"
            )
            
            return response
        except requests.exceptions.RequestException as e:
            elapsed_time = time.time() - start_time
            self.logger.error(f"请求异常: {str(e)}, 用时: {elapsed_time:.2f}秒")
            raise
            
    def get(self, url: str, params: Optional[Dict] = None, **kwargs) -> requests.Response:
        """
        发送GET请求
        
        Args:
            url: 请求URL
            params: 请求参数字典
            **kwargs: 其他请求参数
            
        Returns:
            requests.Response: 响应对象
        """
        return self.request('GET', url, params=params, **kwargs)
        
    def post(self, url: str, data: Optional[Dict] = None, json: Optional[Dict] = None, **kwargs) -> requests.Response:
        """
        发送POST请求
        
        Args:
            url: 请求URL
            data: 表单数据字典
            json: JSON数据字典
            **kwargs: 其他请求参数
            
        Returns:
            requests.Response: 响应对象
        """
        return self.request('POST', url, data=data, json=json, **kwargs)
        
    def put(self, url: str, data: Optional[Dict] = None, **kwargs) -> requests.Response:
        """
        发送PUT请求
        
        Args:
            url: 请求URL
            data: 表单数据字典
            **kwargs: 其他请求参数
            
        Returns:
            requests.Response: 响应对象
        """
        return self.request('PUT', url, data=data, **kwargs)
        
    def delete(self, url: str, **kwargs) -> requests.Response:
        """
        发送DELETE请求
        
        Args:
            url: 请求URL
            **kwargs: 其他请求参数
            
        Returns:
            requests.Response: 响应对象
        """
        return self.request('DELETE', url, **kwargs)
        
    def get_json(self, url: str, params: Optional[Dict] = None, **kwargs) -> Any:
        """
        发送GET请求并解析JSON响应
        
        Args:
            url: 请求URL
            params: 请求参数字典
            **kwargs: 其他请求参数
            
        Returns:
            Any: 解析后的JSON数据
        """
        response = self.get(url, params=params, **kwargs)
        response.raise_for_status()
        return response.json()
        
    def post_json(self, url: str, data: Optional[Dict] = None, json: Optional[Dict] = None, **kwargs) -> Any:
        """
        发送POST请求并解析JSON响应
        
        Args:
            url: 请求URL
            data: 表单数据字典
            json: JSON数据字典
            **kwargs: 其他请求参数
            
        Returns:
            Any: 解析后的JSON数据
        """
        response = self.post(url, data=data, json=json, **kwargs)
        response.raise_for_status()
        return response.json()
        
    def download_file(self, url: str, local_path: str, chunk_size: int = 8192, **kwargs) -> bool:
        """
        下载文件
        
        Args:
            url: 文件URL
            local_path: 本地保存路径
            chunk_size: 块大小
            **kwargs: 其他请求参数
            
        Returns:
            bool: 下载是否成功
        """
        try:
            # 设置流式下载
            kwargs['stream'] = True
            
            # 发送请求
            response = self.get(url, **kwargs)
            response.raise_for_status()
            
            # 获取文件大小
            file_size = int(response.headers.get('content-length', 0))
            
            # 下载文件
            downloaded_size = 0
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # 记录下载进度
                        if file_size > 0:
                            progress = (downloaded_size / file_size) * 100
                            self.logger.debug(f"下载进度: {progress:.2f}%")
                            
            self.logger.info(f"文件已下载到: {local_path}，大小: {downloaded_size} 字节")
            return True
        except Exception as e:
            self.logger.error(f"文件下载失败: {str(e)}")
            return False
            
    def set_proxy(self, proxy: Optional[Dict[str, str]] = None) -> None:
        """
        设置代理
        
        Args:
            proxy: 代理配置字典，如 {'http': 'http://proxy.example.com:8080', 'https': 'https://proxy.example.com:8080'}
        """
        if proxy:
            self.session.proxies.update(proxy)
        else:
            # 清除代理
            self.session.proxies.clear()
            
    def set_cookies(self, cookies: Dict[str, str], domain: Optional[str] = None) -> None:
        """
        设置Cookies
        
        Args:
            cookies: Cookies字典
            domain: Cookie域名，可选
        """
        for key, value in cookies.items():
            if domain:
                self.session.cookies.set(key, value, domain=domain)
            else:
                self.session.cookies.set(key, value)
                
    def get_cookies(self) -> Dict[str, str]:
        """
        获取当前会话的所有Cookies
        
        Returns:
            Dict[str, str]: Cookies字典
        """
        return {cookie.name: cookie.value for cookie in self.session.cookies}
        
    def clear_cookies(self) -> None:
        """清除所有Cookies"""
        self.session.cookies.clear()
        
    def close(self) -> None:
        """关闭会话"""
        self.session.close()
        
    def __enter__(self) -> 'RequestsManager':
        """上下文管理器入口"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """上下文管理器出口"""
        self.close() 