#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
浏览器工具模块
用于管理浏览器
"""
import logging
import os
import random
from typing import Optional

from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, BrowserContext

from core.config import ConfigManager


class BrowserManager:
    """浏览器管理器，用于管理浏览器实例"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化浏览器管理器
        
        Args:
            config: 配置管理器实例
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config
        
        # 从配置中获取浏览器设置
        self.browser_type = config.get('browser.type', 'chromium')
        self.headless = config.get('browser.headless', True)
        self.slow_mo = config.get('browser.slow_mo', 50)
        self.user_agent = config.get('browser.user_agent')
        self.viewport = config.get('browser.viewport', {'width': 1366, 'height': 768})
        
        # Trace配置
        self.enable_tracing = config.get('browser.enable_tracing', False)
        self.trace_path = config.get('browser.trace_path', 'traces/browser_trace.zip')
        self.trace_options = config.get('browser.trace_options', {
            'screenshots': True,
            'snapshots': True,
            'sources': True
        })
        
        # 确保目录存在
        if self.enable_tracing:
            os.makedirs(os.path.dirname(self.trace_path), exist_ok=True)
            
        # Playwright实例和浏览器实例
        self.playwright = None
        self.browser = None
        self.context = None
        
    def _create_browser(self) -> Browser:
        """
        创建浏览器实例
        
        Returns:
            Browser: 浏览器实例
        """
        self.logger.info(f"正在启动 {self.browser_type} 浏览器，headless={self.headless}")
        
        # 获取浏览器类型
        if self.browser_type == 'chromium':
            browser_factory = self.playwright.chromium
        elif self.browser_type == 'firefox':
            browser_factory = self.playwright.firefox
        elif self.browser_type == 'webkit':
            browser_factory = self.playwright.webkit
        else:
            self.logger.warning(f"未知的浏览器类型: {self.browser_type}，使用默认的chromium")
            browser_factory = self.playwright.chromium
            
        # 启动浏览器
        self.browser = browser_factory.launch(
            headless=self.headless,
            slow_mo=self.slow_mo,
            args=[
                "--disable-blink-features=AutomationControlled",
                "--disable-web-security",
                "--use-angle=swiftshader",
                "--disable-gpu",
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-setuid-sandbox",
                "--disable-software-rasterizer",
                # 新增中文语言参数
                "--lang=zh-CN,zh;q=0.9"
            ]
        )
        
        return self.browser
        
    def save_storage_state(self, path: str) -> bool:
        """
        保存存储状态
        
        Args:
            path: 保存路径
            
        Returns:
            bool: 保存是否成功
        """
        if not self.context:
            self.logger.error("浏览器上下文未创建，无法保存存储状态")
            return False
            
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(path), exist_ok=True)
            
            # 保存存储状态
            self.context.storage_state(path=path)
            self.logger.info(f"存储状态已保存到: {path}")
            return True
        except Exception as e:
            self.logger.error(f"保存存储状态失败: {str(e)}")
            return False
            
    def stop_tracing(self, path: Optional[str] = None) -> bool:
        """
        停止跟踪
        
        Args:
            path: 保存路径，可选，默认使用配置中的路径
            
        Returns:
            bool: 停止是否成功
        """
        if not self.context or not self.enable_tracing:
            return False
            
        try:
            trace_path = path or self.trace_path
            os.makedirs(os.path.dirname(trace_path), exist_ok=True)
            self.context.tracing.stop(path=trace_path)
            self.logger.info(f"跟踪已保存到: {trace_path}")
            return True
        except Exception as e:
            self.logger.error(f"停止跟踪失败: {str(e)}")
            return False

    def __enter__(self) -> BrowserContext:
        try:
            self.playwright = sync_playwright().start()
            self._create_browser()
            self._create_context()
            # 启用 Trace 记录
            if self.enable_tracing:
                self.context.tracing.start(
                    name="tesla_login_trace",
                    screenshots=True,
                    snapshots=True,
                    sources=True
                )
            return self.context
        except Exception as e:
            raise Exception(f"浏览器初始化失败: {str(e)}")

    def __exit__(self, exc_type, exc_val, exc_tb):
        cleanup_order = [
            self._close_context,
            self._close_browser,
            self._stop_playwright
        ]

        # 按顺序执行清理，确保资源释放
        for cleanup in cleanup_order:
            try:
                cleanup()
            except Exception as e:
                logging.error(f"资源清理异常: {str(e)}", exc_info=True)

    # def _launch_browser(self):
    #     """启动浏览器实例"""
    #     self.browser = self.playwright.chromium.launch(
    #         headless=self.headless,
    #         args=[
    #             "--disable-blink-features=AutomationControlled",
    #             "--disable-web-security",
    #             "--use-angle=swiftshader",
    #             "--disable-gpu",
    #             "--no-sandbox",
    #             "--disable-dev-shm-usage",
    #             "--disable-setuid-sandbox",
    #             "--disable-software-rasterizer",
    #             # 新增中文语言参数
    #             "--lang=zh-CN,zh;q=0.9"
    #         ]
    #     )

    def _create_context(self):
        """创建浏览器上下文"""
        self.context = self.browser.new_context(
            user_agent=self.user_agent,
            viewport=self.viewport,
            geolocation={
                "latitude": random.uniform(31.2, 31.3),
                "longitude": random.uniform(121.4, 121.5)
            },
            permissions=["geolocation"],
            color_scheme="dark",
            reduced_motion="reduce",
            has_touch=False,
            # 覆盖默认的 sec-ch-ua 值
            extra_http_headers={
                "sec-ch-ua": '"Chromium";v="133", "Not(A:Brand";v="99"',
                "sec-ch-ua-mobile": "?0",
                "Accept-Language": "zh-CN,zh;q=0.9",
                "account-country": "CN"
            }
        )

    def _close_context(self):
        """关闭浏览器上下文"""
        if hasattr(self, 'context') and self.context:
            # 优先保存 Trace 记录
            if self.enable_tracing:
                self.logger.info(f"💾 保存 Trace 记录到: {self.trace_path}")
                self.context.tracing.stop(path=self.trace_path)

            self.logger.debug("🛑 正在关闭浏览器上下文...")
            self.context.close()
            self.context = None

    def _close_browser(self):
        """关闭浏览器实例"""
        if hasattr(self, 'browser') and self.browser:
            self.logger.debug("🛑 正在关闭浏览器进程...")
            self.browser.close()
            self.browser = None

    def _stop_playwright(self):
        """停止 Playwright 服务"""
        if hasattr(self, 'playwright') and self.playwright:
            self.logger.debug("🛑 正在停止 Playwright 服务...")
            self.playwright.stop()
            self.playwright = None