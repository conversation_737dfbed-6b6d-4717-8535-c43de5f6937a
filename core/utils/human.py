#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
人类行为模拟工具
用于模拟人类操作
"""
import math
import random
import time
from typing import List, Tuple

from playwright.sync_api import Page


class HumanBehaviorSimulator:
    """人类行为模拟器，用于模拟人类操作"""
    
    @staticmethod
    def random_delay(min_ms: int = 300, max_ms: int = 1000) -> None:
        """
        随机延时，模拟人类思考/操作间隙
        
        Args:
            min_ms: 最小延时毫秒数
            max_ms: 最大延时毫秒数
        """
        delay_ms = random.randint(min_ms, max_ms)
        time.sleep(delay_ms / 1000)
        
    @staticmethod
    def bezier_curve(p1: Tuple[float, float], p2: Tuple[float, float], 
                     p3: Tuple[float, float], p4: Tuple[float, float], 
                     num_points: int = 50) -> List[Tuple[float, float]]:
        """
        生成贝塞尔曲线，用于模拟鼠标移动路径
        
        Args:
            p1: 起点坐标
            p2: 控制点1坐标
            p3: 控制点2坐标
            p4: 终点坐标
            num_points: 曲线上的点数
            
        Returns:
            List[Tuple[float, float]]: 曲线上的点列表
        """
        points = []
        for i in range(num_points):
            t = i / (num_points - 1)
            # 三次贝塞尔曲线公式
            x = (1-t)**3 * p1[0] + 3*(1-t)**2*t * p2[0] + 3*(1-t)*t**2 * p3[0] + t**3 * p4[0]
            y = (1-t)**3 * p1[1] + 3*(1-t)**2*t * p2[1] + 3*(1-t)*t**2 * p3[1] + t**3 * p4[1]
            points.append((x, y))
        return points
        
    @staticmethod
    def random_bezier_curve(start: Tuple[float, float], end: Tuple[float, float],
                            num_points: int = 50) -> List[Tuple[float, float]]:
        """
        生成随机贝塞尔曲线，用于模拟真实鼠标移动
        
        Args:
            start: 起点坐标
            end: 终点坐标
            num_points: 曲线上的点数
            
        Returns:
            List[Tuple[float, float]]: 曲线上的点列表
        """
        # 计算控制点，添加一些随机性
        dx, dy = end[0] - start[0], end[1] - start[1]
        dist = math.sqrt(dx*dx + dy*dy)
        
        # 控制点的偏移量，与距离成正比
        offset = dist * 0.3
        
        # 控制点1
        angle1 = random.uniform(0, 2*math.pi)
        p2 = (
            start[0] + offset * math.cos(angle1),
            start[1] + offset * math.sin(angle1)
        )
        
        # 控制点2
        angle2 = random.uniform(0, 2*math.pi)
        p3 = (
            end[0] + offset * math.cos(angle2),
            end[1] + offset * math.sin(angle2)
        )
        
        return HumanBehaviorSimulator.bezier_curve(start, p2, p3, end, num_points)
        
    @staticmethod
    def realistic_click(page: Page, element, current_pos: Tuple[float, float] = None) -> Tuple[float, float]:
        """
        模拟真实的鼠标点击
        
        Args:
            page: Playwright页面对象
            element: 要点击的元素
            current_pos: 当前鼠标位置，如果为None则使用页面中心
            
        Returns:
            Tuple[float, float]: 点击后的鼠标位置
        """
        # 获取元素的边界框
        bbox = element.bounding_box()
        if not bbox:
            raise ValueError("元素不可见或不存在")
            
        # 计算目标点，元素中心点附近的随机位置
        target_x = bbox['x'] + bbox['width'] * random.uniform(0.4, 0.6)
        target_y = bbox['y'] + bbox['height'] * random.uniform(0.4, 0.6)
        target_pos = (target_x, target_y)
        
        # 如果未提供当前位置，使用页面中心
        if current_pos is None:
            viewport = page.viewport_size
            current_pos = (viewport['width'] // 2, viewport['height'] // 2)
            
        # 生成鼠标移动路径
        move_path = HumanBehaviorSimulator.random_bezier_curve(current_pos, target_pos)
        
        # 模拟鼠标移动
        for point in move_path:
            page.mouse.move(point[0], point[1])
            HumanBehaviorSimulator.random_delay(5, 15)  # 更短的延时
            
        # 模拟鼠标点击前的短暂停顿
        HumanBehaviorSimulator.random_delay(50, 150)
        
        # 点击
        page.mouse.click(target_pos[0], target_pos[1])
        
        # 点击后的随机停顿
        HumanBehaviorSimulator.random_delay(100, 300)
        
        return target_pos
        
    @staticmethod
    def realistic_type(element, text: str, error_rate: float = 0.05) -> None:
        """
        模拟真实的键盘输入
        
        Args:
            element: 要输入的元素
            text: 要输入的文本
            delay_range: 按键之间的延时范围（毫秒）
        """
        element.click()
        
        # 清空输入框
        element.fill("")
        
        # 等待一下
        HumanBehaviorSimulator.random_delay(200, 500)
        
        # 逐个字符输入
        """拟真输入"""
        element.click()
        for char in text:
            element.type(char, delay=random.randint(50, 150))
            if random.random() < error_rate:
                element.press("Backspace")
                HumanBehaviorSimulator.random_delay(50, 100)
                element.type(char)
            if random.random() < 0.1:
                HumanBehaviorSimulator.random_delay(1000, 2000)
        
        # 输入完成后的停顿
        HumanBehaviorSimulator.random_delay(200, 500)
        
    @staticmethod
    def random_scroll(page: Page, min_distance: int = 100, max_distance: int = 300,  direction: str = 'down', smooth: bool = True) -> None:
        """
        模拟随机滚动
        
        Args:
            page: Playwright页面对象
            min_distance: 最小滚动距离
            max_distance: 最大滚动距离
            direction: 滚动方向，'up'或'down'
            smooth: 是否平滑滚动
        """
        # 随机滚动距离
        distance = random.randint(min_distance, max_distance)
        
        # 根据方向调整符号
        if direction == 'up':
            distance = -distance
            
        if smooth:
            # 平滑滚动，分多次进行
            steps = random.randint(5, 15)
            step_distance = distance / steps
            
            for _ in range(steps):
                page.mouse.wheel(0, step_distance)
                HumanBehaviorSimulator.random_delay(30, 100)
        else:
            # 直接滚动
            page.mouse.wheel(0, distance)
            
        # 滚动后等待页面加载
        HumanBehaviorSimulator.random_delay(300, 800) 