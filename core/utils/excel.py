#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Excel处理工具模块
用于处理Excel文件
"""
import logging
import os
from typing import Dict, List, Optional, Union

import openpyxl
import pandas as pd
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter

from core.config import ConfigManager


class ExcelManager:
    """Excel管理器，用于处理Excel文件"""
    
    def __init__(self, config: ConfigManager = None):
        """
        初始化Excel管理器
        
        Args:
            config: 配置管理器实例，可选
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config
        
    def read_excel(self, file_path: str, sheet_name: Optional[Union[str, int, List]] = 0,
                  **kwargs) -> Union[pd.DataFrame, Dict[str, pd.DataFrame]]:
        """
        读取Excel文件
        
        Args:
            file_path: Excel文件路径
            sheet_name: 工作表名称或索引，默认为第一个工作表
            **kwargs: 其他pandas.read_excel参数
            
        Returns:
            Union[pd.DataFrame, Dict[str, pd.DataFrame]]: 
                如果sheet_name是单个值，返回DataFrame，
                如果sheet_name是列表或None，返回字典，键为sheet名称
        """
        if not os.path.exists(file_path):
            self.logger.error(f"文件不存在: {file_path}")
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        try:
            self.logger.info(f"读取Excel文件: {file_path}")
            df = pd.read_excel(file_path, sheet_name=sheet_name, **kwargs)
            
            if isinstance(df, pd.DataFrame):
                self.logger.info(f"已读取 {len(df)} 行数据")
            else:
                for sheet, sheet_df in df.items():
                    self.logger.info(f"工作表 '{sheet}' 已读取 {len(sheet_df)} 行数据")
                    
            return df
        except Exception as e:
            self.logger.error(f"读取Excel文件失败: {str(e)}")
            raise
            
    def write_excel(self, data: Union[pd.DataFrame, Dict[str, pd.DataFrame]],  file_path: str, **kwargs) -> bool:
        """
        写入Excel文件
        
        Args:
            data: 数据，可以是DataFrame或多个DataFrame的字典
            file_path: 保存路径
            **kwargs: 其他pandas.to_excel参数
            
        Returns:
            bool: 写入是否成功
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
            
            # 如果是单个DataFrame
            if isinstance(data, pd.DataFrame):
                self.logger.info(f"正在写入 {len(data)} 行数据到Excel文件: {file_path}")
                data.to_excel(file_path, **kwargs)
            # 如果是多个DataFrame的字典
            elif isinstance(data, dict):
                self.logger.info(f"正在写入多个工作表到Excel文件: {file_path}")
                with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                    for sheet_name, df in data.items():
                        self.logger.info(f"写入工作表 '{sheet_name}', {len(df)} 行数据")
                        df.to_excel(writer, sheet_name=sheet_name, **kwargs)
            else:
                self.logger.error(f"不支持的数据类型: {type(data)}")
                return False
                
            self.logger.info(f"Excel文件已保存到: {file_path}")
            return True
        except Exception as e:
            self.logger.error(f"写入Excel文件失败: {str(e)}")
            return False
            
    def dataframe_to_list(self, df: pd.DataFrame, include_headers: bool = True) -> List[List]:
        """
        将DataFrame转换为二维列表
        
        Args:
            df: DataFrame对象
            include_headers: 是否包含表头
            
        Returns:
            List[List]: 二维列表
        """
        if include_headers:
            result = [df.columns.tolist()]
            result.extend(df.values.tolist())
        else:
            result = df.values.tolist()
            
        return result
        
    def style_excel(self, file_path: str, sheet_name: Optional[str] = None,
                   header_style: Optional[Dict] = None, 
                   auto_filter: bool = True,
                   auto_width: bool = True) -> bool:
        """
        美化Excel文件样式
        
        Args:
            file_path: Excel文件路径
            sheet_name: 要处理的工作表名称，如果为None则处理所有工作表
            header_style: 表头样式字典，可包含font_bold, font_size, bg_color等
            auto_filter: 是否自动添加筛选
            auto_width: 是否自动调整列宽
            
        Returns:
            bool: 处理是否成功
        """
        if not os.path.exists(file_path):
            self.logger.error(f"文件不存在: {file_path}")
            return False
            
        try:
            # 默认表头样式
            default_header_style = {
                'font_bold': True,
                'font_size': 11,
                'bg_color': 'D9E1F2',  # 浅蓝色
                'align_horizontal': 'center',
                'align_vertical': 'center',
                'border': True
            }
            
            # 合并默认样式和自定义样式
            if header_style:
                default_header_style.update(header_style)
                
            # 加载工作簿
            wb = openpyxl.load_workbook(file_path)
            
            # 需要处理的工作表
            sheets_to_process = [sheet_name] if sheet_name else wb.sheetnames
            
            for sheet_name in sheets_to_process:
                if sheet_name not in wb:
                    self.logger.warning(f"工作表不存在: {sheet_name}")
                    continue
                    
                ws = wb[sheet_name]
                
                # 获取有数据的最大行列数
                max_row = ws.max_row
                max_col = ws.max_column
                
                if max_row == 0 or max_col == 0:
                    self.logger.warning(f"工作表 '{sheet_name}' 没有数据")
                    continue
                    
                # 设置表头样式
                header_font = Font(
                    bold=default_header_style['font_bold'],
                    size=default_header_style['font_size']
                )
                
                header_alignment = Alignment(
                    horizontal=default_header_style['align_horizontal'],
                    vertical=default_header_style['align_vertical'],
                    wrap_text=True
                )
                
                header_fill = PatternFill(
                    start_color=default_header_style['bg_color'].replace('#', ''),
                    end_color=default_header_style['bg_color'].replace('#', ''),
                    fill_type='solid'
                )
                
                if default_header_style['border']:
                    thin_border = Border(
                        left=Side(style='thin'),
                        right=Side(style='thin'),
                        top=Side(style='thin'),
                        bottom=Side(style='thin')
                    )
                else:
                    thin_border = None
                    
                # 应用表头样式
                for col in range(1, max_col + 1):
                    cell = ws.cell(row=1, column=col)
                    cell.font = header_font
                    cell.alignment = header_alignment
                    cell.fill = header_fill
                    if thin_border:
                        cell.border = thin_border
                        
                # 自动筛选
                if auto_filter and max_row > 1:
                    ws.auto_filter.ref = f"A1:{get_column_letter(max_col)}{max_row}"
                    
                # 自动调整列宽
                if auto_width:
                    for column in range(1, max_col + 1):
                        column_letter = get_column_letter(column)
                        max_length = 0
                        for cell in ws[column_letter]:
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass
                        adjusted_width = (max_length + 2) * 1.2
                        ws.column_dimensions[column_letter].width = min(adjusted_width, 30)
                
                self.logger.info(f"已应用样式到工作表 '{sheet_name}'")
                
            # 保存工作簿
            wb.save(file_path)
            self.logger.info(f"Excel样式已更新并保存到: {file_path}")
            return True
        except Exception as e:
            self.logger.error(f"设置Excel样式失败: {str(e)}")
            return False
            
    def merge_excel_files(self, file_paths: List[str], output_path: str, sheet_mapping: Optional[Dict[str, str]] = None) -> bool:
        """
        合并多个Excel文件
        
        Args:
            file_paths: 要合并的Excel文件路径列表
            output_path: 输出文件路径
            sheet_mapping: 工作表映射，格式为 {输出工作表名: 输入工作表名}，默认使用相同名称
            
        Returns:
            bool: 合并是否成功
        """
        try:
            # 检查输入文件
            for file_path in file_paths:
                if not os.path.exists(file_path):
                    self.logger.error(f"文件不存在: {file_path}")
                    return False
                    
            # 创建结果字典
            result_dict = {}
            
            # 读取并合并每个文件
            for file_path in file_paths:
                self.logger.info(f"处理文件: {file_path}")
                
                # 读取所有工作表
                excel_data = pd.read_excel(file_path, sheet_name=None)
                
                # 处理每个工作表
                for sheet_name, df in excel_data.items():
                    # 确定目标工作表名称
                    target_sheet = sheet_name
                    if sheet_mapping and sheet_name in sheet_mapping:
                        target_sheet = sheet_mapping[sheet_name]
                        
                    # 合并到结果字典
                    if target_sheet in result_dict:
                        result_dict[target_sheet] = pd.concat(
                            [result_dict[target_sheet], df], 
                            ignore_index=True
                        )
                    else:
                        result_dict[target_sheet] = df
                        
            # 检查是否有数据
            if not result_dict:
                self.logger.warning("没有发现任何数据")
                return False
                
            # 写入合并后的数据
            return self.write_excel(result_dict, output_path)
        except Exception as e:
            self.logger.error(f"合并Excel文件失败: {str(e)}")
            return False
            
    def compare_excel_files(self, file1_path: str, file2_path: str, 
                           key_columns: Union[str, List[str]],
                           sheet_name: Optional[str] = None) -> Dict[str, pd.DataFrame]:
        """
        比较两个Excel文件的差异
        
        Args:
            file1_path: 第一个Excel文件路径
            file2_path: 第二个Excel文件路径
            key_columns: 用于比较的键列，可以是单个列名或列名列表
            sheet_name: 要比较的工作表名称，如果为None则使用默认的第一个工作表
            
        Returns:
            Dict[str, pd.DataFrame]: 包含'只在文件1中', '只在文件2中', '共有但不同'三个DataFrame的字典
        """
        try:
            # 读取Excel文件
            df1 = self.read_excel(file1_path, sheet_name=sheet_name)
            df2 = self.read_excel(file2_path, sheet_name=sheet_name)
            
            # 如果返回字典，取第一个工作表
            if isinstance(df1, dict):
                sheet_names = list(df1.keys())
                if not sheet_name and sheet_names:
                    sheet_name = sheet_names[0]
                df1 = df1[sheet_name]
                
            if isinstance(df2, dict):
                sheet_names = list(df2.keys())
                if not sheet_name and sheet_names:
                    sheet_name = sheet_names[0]
                df2 = df2[sheet_name]
            
            # 将键列转换为列表
            if isinstance(key_columns, str):
                key_columns = [key_columns]
                
            # 检查键列是否存在
            for col in key_columns:
                if col not in df1.columns:
                    self.logger.error(f"文件1中不存在列: {col}")
                    raise ValueError(f"文件1中不存在列: {col}")
                if col not in df2.columns:
                    self.logger.error(f"文件2中不存在列: {col}")
                    raise ValueError(f"文件2中不存在列: {col}")
            
            # 设置索引
            df1_indexed = df1.set_index(key_columns)
            df2_indexed = df2.set_index(key_columns)
            
            # 找出差异
            only_in_df1 = df1_indexed.loc[~df1_indexed.index.isin(df2_indexed.index)]
            only_in_df2 = df2_indexed.loc[~df2_indexed.index.isin(df1_indexed.index)]
            
            # 找出共有但内容不同的行
            common_index = df1_indexed.index.intersection(df2_indexed.index)
            df1_common = df1_indexed.loc[common_index]
            df2_common = df2_indexed.loc[common_index]
            
            # 获取共有列
            common_columns = list(set(df1.columns).intersection(set(df2.columns)))
            common_columns = [col for col in common_columns if col not in key_columns]
            
            # 比较共有行共有列的内容
            different_rows = []
            for idx in common_index:
                row1 = df1_common.loc[idx]
                row2 = df2_common.loc[idx]
                if not row1[common_columns].equals(row2[common_columns]):
                    different_rows.append(idx)
            
            # 重置索引并返回结果
            result = {
                '只在文件1中': only_in_df1.reset_index(),
                '只在文件2中': only_in_df2.reset_index(),
                '共有但不同': df1_common.loc[different_rows].reset_index()
            }
            
            self.logger.info(f"比较完成: 只在文件1中: {len(only_in_df1)}行, "
                            f"只在文件2中: {len(only_in_df2)}行, "
                            f"共有但不同: {len(different_rows)}行")
            
            return result
        except Exception as e:
            self.logger.error(f"比较Excel文件失败: {str(e)}")
            raise 