#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置管理模块
负责管理和读取配置
"""
import os
from typing import Any, Dict

import yaml

from core.base import BaseObject


class ConfigManager(BaseObject):
    """配置管理器，负责加载和管理配置"""
    
    def __init__(self, config_path: str = None, env: str = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，默认为None，将使用默认配置路径
            env: 环境标识，默认为None，将从配置文件中读取
        """
        # 初始化基础对象
        super().__init__()
        
        self.config_data = {}
        self.env_config_data = {}
        
        # 默认配置路径
        self.base_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
        self.default_config_path = os.path.join(self.base_path, 'conf', 'config.yaml')
        
        # 加载配置
        self.config_path = config_path or self.default_config_path
        self.reload()
        
        # 设置环境
        self.env = env or self.config_data.get('env', 'dev')
        self.logger.info(f"当前运行环境: {self.env}")
        
        # 加载环境配置
        self.load_env_config()
        
    def reload(self) -> bool:
        """
        重新加载配置
        
        Returns:
            bool: 加载是否成功
        """
        try:
            self.logger.info(f"正在加载配置文件: {self.config_path}")
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config_data = yaml.safe_load(f) or {}
            self.logger.info(f"配置加载成功")
            return True
        except (yaml.YAMLError, IOError) as e:
            self.logger.error(f"配置加载失败: {str(e)}")
            return False
    
    def load_env_config(self) -> bool:
        """
        加载环境特定配置
        
        Returns:
            bool: 加载是否成功
        """
        env_config_path = os.path.join(self.base_path, 'conf', 'env', f"{self.env}.yaml")
        
        if not os.path.exists(env_config_path):
            self.logger.warning(f"环境配置文件不存在: {env_config_path}，将使用默认配置")
            return False
            
        try:
            self.logger.info(f"正在加载环境配置文件: {env_config_path}")
            with open(env_config_path, 'r', encoding='utf-8') as f:
                self.env_config_data = yaml.safe_load(f) or {}
            self.logger.info(f"环境配置加载成功")
            return True
        except (yaml.YAMLError, IOError) as e:
            self.logger.error(f"环境配置加载失败: {str(e)}")
            return False
            
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，优先从环境配置中获取，如果不存在则从主配置中获取
        
        Args:
            key: 配置键，支持点号分隔的多级键
            default: 默认值，当键不存在时返回
            
        Returns:
            Any: 配置值
        """
        # 首先从环境配置中查找
        env_value = self._get_from_dict(self.env_config_data, key)
        if env_value is not None:
            return env_value
            
        # 如果环境配置中不存在，则从主配置中查找
        return self._get_from_dict(self.config_data, key, default)
    
    def _get_from_dict(self, data: Dict, key: str, default: Any = None) -> Any:
        """
        从指定的字典中获取值
        
        Args:
            data: 数据字典
            key: 配置键，支持点号分隔的多级键
            default: 默认值，当键不存在时返回
            
        Returns:
            Any: 配置值
        """
        parts = key.split('.')
        current = data
        
        for part in parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return default
                
        return current
        
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值，只更新主配置
        
        Args:
            key: 配置键，支持点号分隔的多级键
            value: 配置值
        """
        parts = key.split('.')
        data = self.config_data
        
        for i, part in enumerate(parts[:-1]):
            if part not in data:
                data[part] = {}
            elif not isinstance(data[part], dict):
                data[part] = {}
            data = data[part]
            
        data[parts[-1]] = value
        
    def save(self, path: str = None) -> bool:
        """
        保存配置到文件
        
        Args:
            path: 保存路径，默认为当前配置路径
            
        Returns:
            bool: 保存是否成功
        """
        save_path = path or self.config_path
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config_data, f, allow_unicode=True, sort_keys=False)
            self.logger.info(f"配置已保存到: {save_path}")
            return True
        except IOError as e:
            self.logger.error(f"配置保存失败: {str(e)}")
            return False
    
    def get_env(self) -> str:
        """
        获取当前环境
        
        Returns:
            str: 当前环境标识
        """
        return self.env
            
    def load_spider_config(self, spider_name: str) -> Dict:
        """
        加载特定爬虫的配置
        
        Args:
            spider_name: 爬虫名称
            
        Returns:
            Dict: 爬虫配置
        """
        spider_config_path = os.path.join(self.base_path, 'conf', f'{spider_name}_config.yaml')
        
        # 如果爬虫配置文件存在，则加载
        if os.path.exists(spider_config_path):
            try:
                with open(spider_config_path, 'r', encoding='utf-8') as f:
                    spider_config = yaml.safe_load(f) or {}
                self.logger.info(f"已加载 {spider_name} 爬虫配置")
                return spider_config
            except (yaml.YAMLError, IOError) as e:
                self.logger.error(f"爬虫配置加载失败: {str(e)}")
                
        # 如果爬虫配置文件不存在，则尝试从主配置中获取
        self.logger.warning(f"未找到 {spider_name} 爬虫配置文件，尝试从主配置中获取")
        return self.get(f'spiders.{spider_name}', {})
