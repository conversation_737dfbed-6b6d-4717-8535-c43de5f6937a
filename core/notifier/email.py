#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
邮件通知模块
实现邮件发送功能
"""
import os
import smtplib
from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import List, Optional

from core.config import ConfigManager
from core.notifier.base import BaseNotifier


class EmailNotifier(BaseNotifier):
    """邮件通知器，用于发送邮件通知"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化邮件通知器
        
        Args:
            config: 配置管理器实例
        """
        super().__init__(config)
        
        # 从配置中获取SMTP设置
        self.smtp_server = config.get('email.smtp_server')
        self.smtp_port = config.get('email.smtp_port', 25)
        self.smtp_ssl = config.get('email.smtp_ssl', False)
        self.smtp_user = config.get('email.sender_email')
        self.smtp_password = config.get('email.sender_password')
        self.sender_name = config.get('email.sender_name', '工单爬虫系统')
        
        # 从配置中获取收件人列表
        self.receivers = config.get('email.receiver_emails', [])
        
        # 检查是否启用
        self.enabled = bool(self.smtp_server and self.smtp_user and 
                            self.smtp_password and self.receivers)
        
        if self.enabled:
            self.logger.info(f"邮件通知器已启用，SMTP服务器: {self.smtp_server}:{self.smtp_port}")
        else:
            self.logger.warning("邮件通知器未启用，缺少必要配置")
            
    def send_message(self, title: str, content: str, **kwargs) -> bool:
        """
        发送普通消息
        
        Args:
            title: 邮件主题
            content: 邮件内容
            **kwargs: 其他参数
                - receivers: 可选，收件人列表，覆盖默认收件人
                - content_type: 可选，内容类型，默认为'plain'，可选'html'
                - cc: 可选，抄送人列表
                - bcc: 可选，密送人列表
            
        Returns:
            bool: 发送是否成功
        """
        if not self.is_enabled():
            self.logger.warning("邮件通知器未启用，无法发送消息")
            return False
            
        # 处理参数
        receivers = kwargs.get('receivers', self.receivers)
        content_type = kwargs.get('content_type', 'plain')
        cc = kwargs.get('cc', [])
        bcc = kwargs.get('bcc', [])
        
        # 创建邮件
        msg = MIMEMultipart()
        msg['Subject'] = title
        msg['From'] = f"{self.sender_name} <{self.smtp_user}>"
        msg['To'] = ', '.join(receivers)
        
        if cc:
            msg['Cc'] = ', '.join(cc)
            receivers.extend(cc)
        
        if bcc:
            receivers.extend(bcc)
            
        # 添加正文
        msg.attach(MIMEText(content, content_type, 'utf-8'))
        
        # 发送邮件
        return self._send_email(msg, receivers)
        
    def send_alert(self, title: str, content: str, error_msg: Optional[str] = None, **kwargs) -> bool:
        """
        发送警告消息
        
        Args:
            title: 邮件主题
            content: 邮件内容
            error_msg: 错误信息
            **kwargs: 其他参数
            
        Returns:
            bool: 发送是否成功
        """
        # 添加警告标记到主题
        alert_title = f"[警告] {title}"
        
        # 构建内容
        alert_content = content
        if error_msg:
            alert_content += f"\n\n错误信息: {error_msg}"
            
        # 设置紧急标记
        kwargs['urgent'] = True
        
        return self.send_message(alert_title, alert_content, **kwargs)
        
    def send_file(self, file_path: str, title: Optional[str] = None, 
                  content: Optional[str] = None, **kwargs) -> bool:
        """
        发送文件
        
        Args:
            file_path: 文件路径
            title: 邮件主题，可选
            content: 邮件内容，可选
            **kwargs: 其他参数
            
        Returns:
            bool: 发送是否成功
        """
        if not self.is_enabled():
            self.logger.warning("邮件通知器未启用，无法发送文件")
            return False
            
        if not os.path.exists(file_path):
            self.logger.error(f"文件不存在: {file_path}")
            return False
            
        # 处理参数
        receivers = kwargs.get('receivers', self.receivers)
        content_type = kwargs.get('content_type', 'plain')
        
        # 创建邮件
        msg = MIMEMultipart()
        msg['Subject'] = title or f"文件: {os.path.basename(file_path)}"
        msg['From'] = f"{self.sender_name} <{self.smtp_user}>"
        msg['To'] = ', '.join(receivers)
        
        # 添加正文
        if content:
            msg.attach(MIMEText(content, content_type, 'utf-8'))
        else:
            msg.attach(MIMEText(f"附件: {os.path.basename(file_path)}", 'plain', 'utf-8'))
            
        # 添加附件
        with open(file_path, 'rb') as f:
            part = MIMEApplication(f.read(), Name=os.path.basename(file_path))
            part['Content-Disposition'] = f'attachment; filename="{os.path.basename(file_path)}"'
            msg.attach(part)
            
        # 发送邮件
        return self._send_email(msg, receivers)
        
    def _send_email(self, msg: MIMEMultipart, receivers: List[str]) -> bool:
        """
        发送邮件
        
        Args:
            msg: 邮件对象
            receivers: 收件人列表
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 创建SMTP连接
            if self.smtp_ssl:
                smtp = smtplib.SMTP_SSL(self.smtp_server, self.smtp_port)
            else:
                smtp = smtplib.SMTP(self.smtp_server, self.smtp_port)
                smtp.starttls()  # 使用TLS加密
                
            # 登录
            smtp.login(self.smtp_user, self.smtp_password)
            
            # 发送
            smtp.sendmail(self.smtp_user, receivers, msg.as_string())
            
            # 关闭连接
            smtp.quit()
            
            self.logger.info(f"邮件发送成功: {msg['Subject']}")
            return True
        except Exception as e:
            self.logger.error(f"邮件发送失败: {str(e)}")
            return False 