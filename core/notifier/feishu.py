#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
飞书通知模块
实现飞书机器人通知功能
"""
import base64
import hashlib
import hmac
import json
import time
from typing import Dict, Optional

import requests

from core.config import ConfigManager
from core.notifier.base import BaseNotifier


class FeishuNotifier(BaseNotifier):
    """飞书通知器，用于发送飞书机器人通知"""
    
    def __init__(self, config: ConfigManager):
        """
        初始化飞书通知器
        
        Args:
            config: 配置管理器实例
        """
        super().__init__(config)
        
        # 从配置中获取飞书设置
        self.webhook = config.get('feishu.webhook')
        self.secret = config.get('feishu.secret')
        
        # 检查是否启用
        self.enabled = config.get('feishu.enable', False) and self.webhook
        
        if self.enabled:
            self.logger.info(f"飞书通知器已启用，WebHook: {self.webhook}")
        else:
            self.logger.warning("飞书通知器未启用，缺少必要配置")
            
    def generate_sign(self, timestamp: int) -> str:
        """
        生成签名
        
        Args:
            timestamp: 时间戳
            
        Returns:
            str: 签名
        """
        if not self.secret:
            return ""
            
        # 拼接时间戳和密钥
        string_to_sign = f"{timestamp}\n{self.secret}"
        
        # 使用HMAC-SHA256加密
        hmac_code = hmac.new(
            string_to_sign.encode("utf-8"),
            digestmod=hashlib.sha256
        ).digest()
        
        # Base64编码
        sign = base64.b64encode(hmac_code).decode('utf-8')
        
        return sign
        
    def send_message(self, title: str, content: str, **kwargs) -> bool:
        """
        发送普通消息
        
        Args:
            title: 消息标题
            content: 消息内容
            **kwargs: 其他参数
                - msg_type: 可选，消息类型，默认为'text'
            
        Returns:
            bool: 发送是否成功
        """
        if not self.is_enabled():
            self.logger.warning("飞书通知器未启用，无法发送消息")
            return False
            
        # 处理参数
        msg_type = kwargs.get('msg_type', 'text')
        
        # 构建消息
        timestamp = int(time.time())
        sign = self.generate_sign(timestamp)
        
        # 根据消息类型构建消息体
        if msg_type == 'text':
            data = {
                "timestamp": timestamp,
                "sign": sign,
                "msg_type": "text",
                "content": {
                    "text": f"{title}\n\n{content}"
                }
            }
        elif msg_type == 'rich_text':
            data = {
                "timestamp": timestamp,
                "sign": sign,
                "msg_type": "post",
                "content": {
                    "post": {
                        "zh_cn": {
                            "title": title,
                            "content": [
                                [
                                    {
                                        "tag": "text",
                                        "text": content
                                    }
                                ]
                            ]
                        }
                    }
                }
            }
        else:
            self.logger.error(f"不支持的消息类型: {msg_type}")
            return False
            
        # 发送请求
        response = self._send_request(data)
        return response and response.get('code') == 0
        
    def send_alert(self, title: str, content: str, error_msg: Optional[str] = None, **kwargs) -> bool:
        """
        发送警告消息
        
        Args:
            title: 消息标题
            content: 消息内容
            error_msg: 错误信息
            **kwargs: 其他参数
            
        Returns:
            bool: 发送是否成功
        """
        # 添加警告标记
        alert_title = f"⚠️ 警告: {title}"
        
        # 构建内容
        alert_content = content
        if error_msg:
            alert_content += f"\n\n错误信息: {error_msg}"
            
        # 使用富文本格式
        kwargs['msg_type'] = 'rich_text'
        
        return self.send_message(alert_title, alert_content, **kwargs)
        
    def send_file(self, file_path: str, title: Optional[str] = None, content: Optional[str] = None, **kwargs) -> bool:
        """
        发送文件
        
        Args:
            file_path: 文件路径
            title: 消息标题，可选
            content: 消息内容，可选
            **kwargs: 其他参数
            
        Returns:
            bool: 发送是否成功
        """
        # 飞书机器人暂不支持直接发送文件
        self.logger.warning("飞书机器人不支持直接发送文件，将发送文件路径")
        
        file_title = title or "文件通知"
        file_content = content or ""
        file_content += f"\n\n文件路径: {file_path}"
        
        return self.send_message(file_title, file_content, **kwargs)
        
    def _send_request(self, data: Dict) -> Optional[Dict]:
        """
        发送HTTP请求
        
        Args:
            data: 请求数据
            
        Returns:
            Optional[Dict]: 响应数据，如果请求失败则返回None
        """
        try:
            headers = {
                'Content-Type': 'application/json; charset=utf-8'
            }
            
            response = requests.post(
                self.webhook,
                headers=headers,
                data=json.dumps(data),
                timeout=5
            )
            
            result = response.json()
            
            if result.get('code') == 0:
                self.logger.info("飞书消息发送成功")
            else:
                self.logger.error(f"飞书消息发送失败: {result.get('msg', 'Unknown error')}")
                
            return result
        except Exception as e:
            self.logger.error(f"飞书请求发送异常: {str(e)}")
            return None 