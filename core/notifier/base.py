#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
通知基类模块
定义通知的接口
"""
import abc
import logging
from typing import Optional

from core.config import ConfigManager


class BaseNotifier(abc.ABC):
    """
    通知基类，定义通知的接口
    每个通知子类都必须继承并实现这些方法
    """
    
    def __init__(self, config: ConfigManager):
        """
        初始化通知器
        
        Args:
            config: 配置管理器实例
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.enabled = False
        
    def is_enabled(self) -> bool:
        """
        检查通知器是否启用
        
        Returns:
            bool: 通知器是否启用
        """
        return self.enabled
        
    @abc.abstractmethod
    def send_message(self, title: str, content: str, **kwargs) -> bool:
        """
        发送普通消息
        
        Args:
            title: 消息标题
            content: 消息内容
            **kwargs: 其他参数
            
        Returns:
            bool: 发送是否成功
        """
        pass
        
    @abc.abstractmethod
    def send_alert(self, title: str, content: str, error_msg: Optional[str] = None, **kwargs) -> bool:
        """
        发送警告消息
        
        Args:
            title: 消息标题
            content: 消息内容
            error_msg: 错误信息
            **kwargs: 其他参数
            
        Returns:
            bool: 发送是否成功
        """
        pass
        
    @abc.abstractmethod
    def send_file(self, file_path: str, title: Optional[str] = None, 
                  content: Optional[str] = None, **kwargs) -> bool:
        """
        发送文件
        
        Args:
            file_path: 文件路径
            title: 消息标题，可选
            content: 消息内容，可选
            **kwargs: 其他参数
            
        Returns:
            bool: 发送是否成功
        """
        pass 