#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
爬虫主入口模块
负责启动和管理各种爬虫
"""
import argparse
import logging
import os
import sys

# 添加当前目录到PATH
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.config import ConfigManager
from core.logger import configure_logging
from spiders.tesla.tesla_spider import TeslaSpider


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='News Spider 爬虫系统')
    parser.add_argument('--spider', type=str, default='tesla', help='要运行的爬虫名称')
    parser.add_argument('--config', type=str, default='conf/config.yaml', help='配置文件路径')
    parser.add_argument('--env', type=str, default=None, 
                        choices=['dev', 'test', 'prod'], 
                        help='运行环境，默认从配置文件读取，可选: dev(开发环境), test(测试环境), prod(生产环境)')
    parser.add_argument('--log-level', type=str, default=None,
                        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                        help='日志级别')
    parser.add_argument('--sync', action='store_true', help='同步到News系统')
    parser.add_argument('--report', action='store_true', help='生成报表')
    parser.add_argument('--no-notify', action='store_true', help='不发送通知')
    
    return parser.parse_args()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 输出启动信息
    print(f"加载配置文件: {args.config}")
    if args.env:
        print(f"指定运行环境: {args.env}")
    print(f"指定的日志级别: {args.log_level if args.log_level else '使用配置文件设置'}")
    
    # 创建ConfigManager并加载配置
    config_manager = ConfigManager(args.config, args.env)
    
    # 如果命令行指定了日志级别，则覆盖配置中的设置
    if args.log_level:
        # 修改配置对象中的日志级别
        config_manager.set('logging.level', args.log_level)
        print(f"命令行指定日志级别: {args.log_level}")
    
    # 配置日志系统
    configure_logging(config_manager)
    
    # 获取主程序日志器
    logger = logging.getLogger("main")
    
    # 记录配置文件和环境信息
    logger.info(f"成功加载配置文件: {args.config}")
    logger.info(f"当前运行环境: {config_manager.get_env()}")
    logger.debug("测试DEBUG级别日志能否正常显示")  # 添加测试日志

    # 根据参数选择爬虫
    if args.spider == 'tesla':
        logger.info("启动特斯拉爬虫")
        spider = TeslaSpider(config_manager)
    else:
        logger.error(f"未知的爬虫: {args.spider}")
        sys.exit(1)
    
    # 执行爬虫
    try:
        # 运行爬虫
        success = spider.run()
        
        if not success:
            logger.error("爬虫运行失败")
            sys.exit(1)
            
        # # 根据参数决定是否同步
        # if args.sync:
        #     logger.info("同步数据到News系统")
        #     sync_success = spider.sync_to_news()
        #     if not sync_success:
        #         logger.warning("同步部分或全部失败")
        #
        # # 根据参数决定是否生成报表
        # if args.report:
        #     logger.info("生成报表")
        #     report_path = spider.generate_report()
        #     if report_path:
        #         logger.info(f"报表已生成: {report_path}")
        #     else:
        #         logger.warning("报表生成失败")
                
        logger.info("爬虫任务执行完成")
        
    except Exception as e:
        logger.error(f"爬虫执行过程中发生异常: {str(e)}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main() 