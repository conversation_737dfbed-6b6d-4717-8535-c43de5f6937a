#!/bin/bash
#
# Playwright缺失依赖安装脚本
# 针对Alibaba Linux系统安装缺失的库文件
#

# 显示执行的命令
set -x

# 错误时退出
set -e

echo "===== 安装Playwright缺失依赖库 ====="
echo "开始时间: $(date)"

# 检查root权限
if [ "$EUID" -ne 0 ]; then
  echo "请使用root权限运行此脚本（如使用sudo）"
  exit 1
fi

# 安装基础依赖
echo "安装基础依赖包..."
yum install -y \
    pango pango-devel \
    atk atk-devel \
    at-spi2-atk at-spi2-atk-devel \
    at-spi2-core at-spi2-core-devel \
    cairo cairo-devel \
    libdrm libdrm-devel \
    libX11 libX11-devel \
    libXcomposite libXcomposite-devel \
    libXdamage libXdamage-devel \
    libXext libXext-devel \
    libXfixes libXfixes-devel \
    libXrandr libXrandr-devel \
    mesa-libgbm mesa-libgbm-devel \
    libxcb libxcb-devel \
    alsa-lib alsa-lib-devel \
    cups-libs \
    libXScrnSaver libXScrnSaver-devel \
    libXtst libXtst-devel \
    libXi libXi-devel \
    libXcursor libXcursor-devel \
    mesa-libEGL mesa-libGL \
    nss dbus-glib

# 特别处理libatspi库
if ! ldconfig -p | grep -q libatspi.so.0; then
    echo "安装libatspi库..."
    # 尝试找到相关包
    yum provides "*/libatspi.so.0" || echo "无法找到libatspi.so.0的提供者"
    # 尝试安装at-spi库（可能的替代方案）
    yum install -y at-spi*
fi

# 安装额外的渲染依赖
echo "安装额外的渲染库..."
yum install -y \
    gtk3 gtk3-devel \
    gdk-pixbuf2 gdk-pixbuf2-devel \
    glib2 glib2-devel \
    dbus dbus-devel \
    adwaita-icon-theme \
    vulkan \
    mesa-vulkan-drivers

# 安装字体
echo "安装字体..."
yum install -y \
    liberation-fonts \
    liberation-fonts-common \
    liberation-mono-fonts \
    liberation-sans-fonts \
    liberation-serif-fonts \
    wqy-microhei-fonts \
    wqy-zenhei-fonts \
    xorg-x11-fonts-100dpi \
    xorg-x11-fonts-75dpi \
    xorg-x11-fonts-Type1 \
    xorg-x11-fonts-misc

# 创建symlinks（如果需要）
echo "创建可能需要的符号链接..."
# libpangocairo
if [ -f /usr/lib64/libpangocairo-1.0.so.0 ] && [ ! -f /usr/lib64/libpangocairo-1.0.so ]; then
    ln -sf /usr/lib64/libpangocairo-1.0.so.0 /usr/lib64/libpangocairo-1.0.so
fi

# libpango
if [ -f /usr/lib64/libpango-1.0.so.0 ] && [ ! -f /usr/lib64/libpango-1.0.so ]; then
    ln -sf /usr/lib64/libpango-1.0.so.0 /usr/lib64/libpango-1.0.so
fi

# 运行ldconfig更新库缓存
echo "更新库缓存..."
ldconfig

echo "===== 依赖安装完成 ====="
echo "时间: $(date)"

# 列出缺失的库（用于验证）
echo "检查是否仍有缺失的库..."
MISSING_LIBS=(
    "libatk-1.0.so.0" 
    "libatk-bridge-2.0.so.0"
    "libdrm.so.2"
    "libatspi.so.0"
    "libX11.so.6"
    "libXcomposite.so.1"
    "libXdamage.so.1"
    "libXext.so.6"
    "libXfixes.so.3"
    "libXrandr.so.2"
    "libgbm.so.1"
    "libxcb.so.1"
    "libpango-1.0.so.0"
    "libcairo.so.2"
    "libasound.so.2"
)

for lib in "${MISSING_LIBS[@]}"; do
    if ldconfig -p | grep -q "$lib"; then
        echo "✓ $lib 已安装"
    else
        echo "✗ $lib 仍然缺失"
    fi
done

echo "完成库检查。如果仍有缺失的库，可能需要手动安装。"
echo ""
echo "安装完成后，请尝试重新运行测试脚本：source .env && python3.9 test_py39_playwright.py" 